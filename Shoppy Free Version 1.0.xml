<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:css='false' b:defaultwidgetversion='2' b:layoutsVersion='3' b:responsive='true' b:templateVersion='1.0.0' expr:class='data:blog.languageDirection' expr:dir='data:blog.languageDirection' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
  <head>
    <meta content='width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1' name='viewport'/>
    <title><data:view.title.escaped/></title>
    <b:include data='blog' name='all-head-content'/>
  <b:if cond='data:view.isHomepage'>
 <script type='application/ld+json'>{&quot;@context&quot;:&quot;http://schema.org&quot;,&quot;@type&quot;:&quot;WebSite&quot;,&quot;name&quot;:&quot;<data:view.title.escaped/>&quot;,&quot;url&quot;:&quot;<data:view.url.canonical/>&quot;,&quot;potentialAction&quot;:{&quot;@type&quot;:&quot;SearchAction&quot;,&quot;target&quot;:&quot;<data:view.url.canonical/>search?q={search_term_string}&quot;,&quot;query-input&quot;:&quot;required name=search_term_string&quot;}}</script>
    </b:if>
    <!-- Google Fonts -->
    <link href='//fonts.googleapis.com/css?family=Damion:400,500,600,700,800.900|Poppins:400,400i,500,500i,700,700i' media='all' rel='stylesheet' type='text/css'/>
    <link href='//fonts.googleapis.com/css?family=Lato:400,500,600,700,800.900|Poppins:400,400i,500,500i,700,700i' media='all' rel='stylesheet' type='text/css'/>
    <link href='https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css' rel='stylesheet'/>
    <link href='https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css' rel='stylesheet'/>
<!-- Template Style CSS -->
<b:skin version='1.0.0'><![CDATA[/* 
-----------------------------------------------
Blogger Template Style
Name:        MASARA X
License:     Premium Version
Version:     1.0
Author:      Masarax
Author Url:  https://www.masarax.com/
----------------------------------------------- */

/*
<!-- Variable definitions -->
<Variable name="keycolor" description="Main Color" type="color" hideEditor="true" default="$(main.color)" value="#dfc78f"/>
<Variable name="followByEmail" description="Follow By Email Text" type="string" hideEditor="true" default="Get all latest content delivered straight to your inbox." value="Get all latest content delivered straight to your inbox."/>

<Group description="Theme Colors" selector="body">
  <Variable name="main.color" description="Theme Color" type="color" default="#003ee6" value="#dfc78f"/>
  <Variable name="dark.color" description="Dark Color" type="color" default="#232323" value="#c8b27e"/>
   <Variable name="dark.color.two" description="Second Dark Color" type="color" default="#121158" value="#181818"/>
  <Variable name="main.menu.color" description="Main Menu Color" type="color" default="#333333" value="#3b3b3b"/>
  <Variable name="body.text.color" description="Text Color" type="color" default="#888888"  value="#898989"/>
   <Variable name="body.text.two" description="Second Text Color" type="color" default="#515184"  value="#865356"/>
  <Variable name="title.color" description="Title Color" type="color" default="#222222" value="#303030"/>
</Group>

<Group description="Intro Section" selector="#main-intro">
  <Variable name="intro.title" description="Intro Title Color" type="color" default="#232323" value="#303030"/>
  <Variable name="intro.text" description="Intro Text Color" type="color" default="#666666" value="#686868"/>
</Group>

<Group description="Premium Features" selector="body">
  <Variable name="enablePremium" description="Enable Premium Features" type="string" default="false" value="false"/>
</Group>

<!-- Extra Variables -->
<Variable name="body.background.color" description="Body background color" hideEditor="true" type="color" default="#f8f8f8"  value="#f8f8f8"/>
<Variable name="head.color" description="Header background color" hideEditor="true" type="color" default="#ffffff"  value="#ffffff"/>
<Variable name="body.background" description="Background" hideEditor="true" type="background" color="#f8f8f8" default="$(color) url() repeat scroll top left" value="$(color) url() repeat scroll top left"/>
<Variable name="body.link.color" description="Body Link Color" hideEditor="true" type="color" default="$(main.color)"  value="#003ee6"/>
<Variable name="body.text.font" description="Font" hideEditor="true" type="font" default="14px Poppins, sans-serif"  value="14px Poppins, sans-serif"/>
<Variable name="posts.background.color" description="Post background color" hideEditor="true" type="color" default="#f8f8f8"  value="#f8f8f8"/>
<Variable name="tabs.font" description="Font 2" hideEditor="true" type="font" default="14px Poppins, sans-serif"  value="14px Poppins, sans-serif"/>
<Variable name="posts.title.color" description="Post title color" hideEditor="true" type="color" default="#111111"  value="#2a2a2a"/>
<Variable name="posts.text.color" description="Post text color" hideEditor="true" type="color" default="#5E5E5E"  value="#616161"/>
<Variable name="posts.icons.color" description="Post icons color" hideEditor="true" type="color" default="$(main.color)"  value="#003ee6"/>
<Variable name="labels.background.color" description="Label background color" hideEditor="true" type="color" default="$(main.color)"  value="#003ee6"/>
<Variable name="footer.dark.color" description="Footer background color" hideEditor="true" type="color" default="#001d4c"  value="#64002d"/>
*/

/*-- Reset CSS --*/
 /* PRELOADER */
ul.no-posts {
    text-align: center;
}
html .home {
    overflow-x: hidden;
}
#google_translate_element {
    clear: both;
}
#preloader {
position: fixed;
z-index: 1800;
top: 0;
right: 0;
bottom: 0;
left: 0;
width: 100%;
height: 100%;
background:$(main.color);
}
.no-js #preloader,
.oldie #preloader {
display: none
}
#loader {
position: absolute;
top: calc(50% - 1.25em);
left: calc(50% - 1.25em);
}
.loader-main {
display: inline-block;
width: 30px;
height: 30px;
position: relative;
border: 4px solid #Fff;
top: 50%;
animation: loader 2s infinite ease;
}
.loader-inner {
vertical-align: top;
display: inline-block;
width: 100%;
background-color: #fff;
animation: loader-inner 2s infinite ease-in;
}
@keyframes loader {
0% {
transform: rotate(0deg);
}
25% {
transform: rotate(180deg);
}
50% {
transform: rotate(180deg);
}
75% {
transform: rotate(360deg);
}
100% {
transform: rotate(360deg);
}
}
@keyframes loader-inner {
0% {
height: 0%;
}
25% {
height: 0%;
}
50% {
height: 100%;
}
75% {
height: 100%;
}
100% {
height: 0%;
}
}
a,abbr,acronym,address,applet,b,big,blockquote,body,caption,center,cite,code,dd,del,dfn,div,dl,dt,em,fieldset,font,form,h1,h2,h3,h4,h5,h6,html,i,iframe,img,ins,kbd,label,legend,li,object,p,pre,q,s,samp,small,span,strike,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,tt,u,ul,var{
    padding:0;
    border:0;
    outline:0;
    vertical-align:baseline;
    background:0 0;
    text-decoration:none
}
*{
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
form,textarea,input,button{
    -webkit-appearance:none;
    -moz-appearance:none;
    appearance:none;
    border-radius:0
}
dl,ul{
    list-style-position:inside;
    font-weight:400;
    list-style:none
}
ul li{
    list-style:none
}
caption,th{
    text-align:center
}
img{
    border:none;
    position:relative
}
a,a:visited{
    text-decoration:none
}
.post-body a:visited {
color:red;
}
.clearfix{
    clear:both
}
.section,.widget,.widget ul{
    margin:0;
    padding:0
}
a{
    color:$(main.color)
}
a:visited {
color:red;
}
a:hover{
    color:$(title.color)
}
a img{
    border:0
}
abbr{
    text-decoration:none
}
.CSS_LIGHTBOX{
    z-index:999999!important
}
.separator a{
    clear:none!important;
    float:none!important;
    margin-left:0!important;
    margin-right:0!important
}
#navbar-iframe,.widget-item-control,a.quickedit,.home-link,.feed-links{
    display:none!important
}
.center{
    display:table;
    margin:0 auto;
    position:relative
}
.widget > h2,.widget > h3{
    display:none
}
h1,h2,h3,h4,h5,h6 {font-family: 'Nunito', sans-serif;font-weight:700;}
/*-- Body Content CSS --*/
 body{
    background-color:#fff;
    font-family: 'Lato', sans-serif;
    font-size:14px;
    font-weight:400;
    color:$(body.text.color);
    word-wrap:break-word;
    margin:0;
    padding:0
}
 .row{
    width:1140px
}
 #content-wrapper{
    float:left;
    width:100%;
    padding:0 0 35px;
    margin:0;
    background-color: #f3f7fd;
}
.home #content-wrapper {
 margin:0;
 padding-bottom:35px;
}
 .item #content-wrapper{
    margin:40px 0;
    background:#ffffff;
}
 #content-wrapper > .container{
    position:relative;
    margin:0 auto
}
 #main-wrapper{
    overflow:hidden;
    padding:0;
    margin:0 -7px
}
.index #main-wrapper {
    overflow: visible;
    float: left;
    width: 100%;
    margin: 0;
}
 .item #main-wrapper{
    margin:0;
float: left;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
    padding: 0 15px;
    margin: 0;
}
.buttonn {
    margin-top: 20px;
    background:$(main.color);
    color: #fff;
    padding: 10px 15px;
    display: inline-block;
}
.buttonn a{
    color:#fff;
}
 #sidebar-wrapper{
  display:none;
   float: right;
    overflow: hidden;
    width: 33.33333333%;
    box-sizing: border-box;
    word-wrap: break-word;
    padding: 0 15px;
}
.item #sidebar-wrapper {
display:none;
}
.sidebar .widget{
position:relative;
overflow:hidden;
background-color:#fff;
box-sizing:border-box;
padding:0;
margin:0 0 35px
}
.sidebar .widget-title{
position:relative;
float:left;
width:100%;
height:32px;
 background:$(main.color);
display:block;
margin:0 0 20px
}
.sidebar .widget-title > h3{
position:relative;
display:block;
height:32px;
font-size:12px;
color:#fff;
font-weight:700;
line-height:32px;
text-align:center;
text-transform:uppercase;
padding:0 15px;
margin:0;
border-radius:2px 2px 0 0
}
.sidebar .widget-content{
float:left;
width:100%;
margin:0
}
 .list-label li{
    position:relative;
    display:block;
    padding:8px 0;
    border-top:1px dotted #eaeaea
}
 .list-label li:first-child{
    padding:0 0 8px;
    border-top:0
}
 .list-label li:last-child{
    padding-bottom:0;
    border-bottom:0
}
 .list-label li a{
    display:block;
    color:$(title.color);
    font-size:13px;
    font-weight:400;
    text-transform:capitalize;
    transition:color .17s
}
 .list-label li a:before{
    content:"\f105";
    float:left;
    color:$(title.color);
    font-size:12px;
    font-weight:400;
    font-family:FontAwesome;
    margin:0 3px 0 0;
    transition:color .17s
}
 .list-label li a:hover{
    color:$(main.color)
}
 .list-label .label-count{
    position:relative;
    float:right;
    font-weight:400
}
 .cloud-label li{
    position:relative;
    float:left;
    margin:0 5px 5px 0
}
 .cloud-label li a{
    display:block;
    height:26px;
    background-color:#fff;
    color:$(title.color);
    font-size:12px;
    line-height:26px;
    font-weight:400;
    padding:0 10px;
    border:1px solid #eaeaea;
    border-radius:3px;
    transition:all .17s ease
}
 .cloud-label li a:hover{
    color:$(main.color)
}
 .cloud-label .label-count{
    display:none
}
 .sidebar .FollowByEmail > .widget-title > h3{
    margin:0
}
 .FollowByEmail .widget-content{
    position:relative;
    overflow:hidden;
    background-color:$(main.color);
    font-weight:400;
    text-align:center;
    box-sizing:border-box;
    padding:20px
}
 .FollowByEmail .widget-content > h3{
    font-size:18px;
    color:#fff;
    font-weight:700;
    margin:0 0 13px
}
 .FollowByEmail .before-text{
    font-size:13px;
    color:#fff;
    line-height:1.4em;
    margin:0 0 15px;
    display:block;
    padding:0 15px;
    overflow:hidden
}
 .FollowByEmail .follow-by-email-inner{
    position:relative
}
 .FollowByEmail .follow-by-email-inner .follow-by-email-address{
    width:100%;
    height:34px;
    color:#888;
    font-size:11px;
    font-family:inherit;
    text-align:center;
    padding:0 10px;
    margin:0 0 10px;
    box-sizing:border-box;
    border:1px solid #eaeaea;
    transition:ease .17s
}
 .FollowByEmail .follow-by-email-inner .follow-by-email-address:focus{
    border-color:rgba(0,0,0,0.1)
}
 .FollowByEmail .follow-by-email-inner .follow-by-email-submit{
    width:100%;
    height:34px;
    font-family:inherit;
    font-size:11px;
    color:#fff;
    background-color:$(dark.color);
    text-transform:uppercase;
    text-align:center;
    font-weight:700;
    cursor:pointer;
    margin:0;
    border:0;
    transition:opacity .17s
}
 .FollowByEmail .follow-by-email-inner .follow-by-email-submit:hover{
    opacity:.8
}
 #ArchiveList ul.flat li{
    color:$(title.color);
    font-size:13px;
    font-weight:400;
    padding:8px 0;
    border-bottom:1px dotted #eaeaea
}
 #ArchiveList ul.flat li:first-child{
    padding-top:0
}
 #ArchiveList ul.flat li:last-child{
    padding-bottom:0;
    border-bottom:0
}
 #ArchiveList .flat li > a{
    display:block;
    color:$(title.color);
    transition:color .17s
}
 #ArchiveList .flat li > a:hover{
    color:$(main.color)
}
 #ArchiveList .flat li > a:before{
    content:"\f105";
    float:left;
    color:$(title.color);
    font-size:12px;
    font-weight:400;
    font-family:FontAwesome;
    margin:0 3px 0 0;
    display:inline-block;
    transition:color .17s
}
 #ArchiveList .flat li > a > span{
    position:relative;
    float:right;
    font-weight:400
}
 .PopularPosts .post{
    overflow:hidden;
    margin:20px 0 0
}
 .PopularPosts .post:first-child{
    margin:0
}
 .PopularPosts .post-image-link{
    position:relative;
    width:80px;
    height:65px;
    float:left;
    overflow:hidden;
    display:block;
    vertical-align:middle;
    margin:0 12px 0 0
}
 .PopularPosts .post-info{
    overflow:hidden
}
 .PopularPosts .post-title{
    font-size:14px;
    font-weight:500;
    line-height:1.4em;
    margin:0 0 5px
}
 .PopularPosts .post-title a{
    display:block;
    color:$(title.color);
    transition:color .17s
}
 .PopularPosts .post-title a:hover{
    color:$(main.color)
}
 .PopularPosts .post-meta{
    font-size:11px
}
 .PopularPosts .post-date:before{
    font-size:10px
}
 .FeaturedPost .post-image-link{
    display:block;
    position:relative;
    overflow:hidden;
    width:100%;
    height:180px;
    margin:0 0 13px
}
 .FeaturedPost .post-title{
    font-size:18px;
    overflow:hidden;
    font-weight:500;
    line-height:1.4em;
    margin:0 0 10px
}
 .FeaturedPost .post-title a{
    color:$(title.color);
    display:block;
    transition:color .17s ease
}
 .FeaturedPost .post-title a:hover{
    color:$(main.color)
}
 .Text{
    font-size:13px
}
 .contact-form-widget form{
    font-weight:400
}
 .contact-form-name{
    float:left;
    width:100%;
    height:30px;
    font-family:inherit;
    font-size:13px;
    line-height:30px;
    box-sizing:border-box;
    padding:5px 10px;
    margin:0 0 10px;
    border:1px solid #eaeaea
}
 .contact-form-email{
    float:left;
    width:100%;
    height:30px;
    font-family:inherit;
    font-size:13px;
    line-height:30px;
    box-sizing:border-box;
    padding:5px 10px;
    margin:0 0 10px;
    border:1px solid #eaeaea
}
 .contact-form-email-message{
    float:left;
    width:100%;
    font-family:inherit;
    font-size:13px;
    box-sizing:border-box;
    padding:5px 10px;
    margin:0 0 10px;
    border:1px solid #eaeaea
}
 .contact-form-button-submit{
    float:left;
    width:100%;
    height:30px;
    background-color:$(main.color);
    font-size:13px;
    color:#fff;
    line-height:30px;
    cursor:pointer;
    box-sizing:border-box;
    padding:0 10px;
    margin:0;
    border:0;
    transition:background .17s ease
}
 .contact-form-button-submit:hover{
    background-color:$(dark.color)
}
 .contact-form-error-message-with-border{
    float:left;
    width:100%;
    background-color:#fbe5e5;
    font-size:11px;
    text-align:center;
    line-height:11px;
    padding:3px 0;
    margin:10px 0;
    box-sizing:border-box;
    border:1px solid #fc6262
}
 .contact-form-success-message-with-border{
    float:left;
    width:100%;
    background-color:#eaf6ff;
    font-size:11px;
    text-align:center;
    line-height:11px;
    padding:3px 0;
    margin:10px 0;
    box-sizing:border-box;
    border:1px solid #5ab6f9
}
 .contact-form-cross{
    margin:0 0 0 3px
}
 .contact-form-error-message,.contact-form-success-message{
    margin:0
}
 .BlogSearch .search-input{
    float:left;
    width:75%;
    height:30px;
    background-color:#fff;
    font-weight:400;
    font-size:13px;
    line-height:30px;
    box-sizing:border-box;
    padding:5px 10px;
    border:1px solid #eaeaea;
    border-right-width:0
}
 .BlogSearch .search-action{
    float:right;
    width:25%;
    height:30px;
    font-family:inherit;
    font-size:13px;
    line-height:30px;
    cursor:pointer;
    box-sizing:border-box;
    background-color:$(main.color);
    color:#fff;
    padding:0 5px;
    border:0;
    transition:background .17s ease
}
 .BlogSearch .search-action:hover{
    background-color:$(dark.color)
}
 .Profile .profile-img{
    float:left;
    width:80px;
    height:80px;
    margin:0 15px 0 0;
    transition:all .17s ease
}
 .Profile .profile-datablock{
    margin:0
}
 .Profile .profile-data .g-profile{
    display:block;
    font-size:14px;
    color:$(title.color);
    margin:0 0 5px;
    transition:color .17s ease
}
 .Profile .profile-data .g-profile:hover{
    color:$(main.color)
}
 .Profile .profile-info > .profile-link{
    color:$(title.color);
    font-size:11px;
    margin:5px 0 0;
    transition:color .17s ease
}
 .Profile .profile-info > .profile-link:hover{
    color:$(main.color)
}
 .Profile .profile-datablock .profile-textblock{
    display:none
}
 .common-widget .LinkList ul li,.common-widget .PageList ul li{
    width:calc(50% - 5px);
    padding:7px 0 0
}
 .common-widget .LinkList ul li:nth-child(odd),.common-widget .PageList ul li:nth-child(odd){
    float:left
}
 .common-widget .LinkList ul li:nth-child(even),.common-widget .PageList ul li:nth-child(even){
    float:right
}
 .common-widget .LinkList ul li a,.common-widget .PageList ul li a{
    display:block;
    color:$(title.color);
    font-size:13px;
    font-weight:400;
    transition:color .17s ease
}
 .common-widget .LinkList ul li a:hover,.common-widget .PageList ul li a:hover{
    color:$(main.color)
}
 .common-widget .LinkList ul li:first-child,.common-widget .LinkList ul li:nth-child(2),.common-widget .PageList ul li:first-child,.common-widget .PageList ul li:nth-child(2){
    padding:0
}
 .post-image-wrap{
    position:relative;
    display:block
}
 .post-image-link:hover:after,.post-image-wrap:hover .post-image-link:after{
    opacity:1
}
 .post-image-link,.comments .avatar-image-container{
    background-color:#f9f9f9
}
 .post-thumb{
    display:block;
    position:relative;
    width:100%;
    height:100%;
    color:transparent;
    object-fit:cover;
    z-index:1;
   opacity: 0;
    transition:opacity .35s ease,transform .35s ease
}
.post-thumb.lazy-yard {
    opacity: 1;
}
 .widget-title > h3{
    display:none
}
#brand-services-wrap .widget-title {
display:none;
}
#brand-services-wrap .widget-title > h3 {
display: block;
    font-size: 42px;
    color: $(dark.color);
    font-weight: 700;
}
.custom-widget li{
    overflow:hidden;
    margin:20px 0 0
}
 .custom-widget li:first-child{
    padding:0;
    margin:0;
    border:0
}
 .custom-widget .post-image-link{
    position:relative;
    width:80px;
    height:60px;
    float:left;
    overflow:hidden;
    display:block;
    vertical-align:middle;
    margin:0 12px 0 0
}
 .custom-widget .post-info{
    overflow:hidden
}
 .custom-widget .post-title{
    overflow:hidden;
    font-size:14px;
    font-weight:500;
    line-height:1.4em;
    margin:0 0 3px
}
 .custom-widget .post-title a{
    display:block;
    color:#303030;
    transition:color .17s
}
 .custom-widget li:hover .post-title a{
    color:$(main.color)
}
 .custom-widget .post-meta{
    font-size:12px
}
#editorial-wrap .container {
    margin: 0 auto;
}
.editorial-authors {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px 0;
}
.editorial-authors .widget {
    display: block;
    width: 33.33333%;
    padding:0 15px;
    box-sizing: border-box;
    border-radius: 12px;
    text-align: center;
}
.editorial-authors .widget .editorial-avatar-wrap {
    position: relative;
}
.editorial-authors .widget .editorial-avatar-wrap .editorial-avatar {
    display: block;
    width: 100%;
    height: 510px;
    overflow: hidden;
}
.editorial-authors .widget .editorial-avatar-wrap .editorial-avatar img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    margin: 0;
}
.editorial-authors .widget .editorial-avatar-wrap .editorial-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30px 30px 34px;
    text-align: center;
    background-image: -webkit-gradient(linear,left top,left bottom,from(#000),to(transparent));
    background-image: -webkit-linear-gradient(bottom,#000,transparent);
    background-image: -moz-linear-gradient(bottom,#000,transparent);
    background-image: -ms-linear-gradient(bottom,#000,transparent);
    background-image: -o-linear-gradient(bottom,#000,transparent);
    background-image: linear-gradient(bottom,#000,transparent);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#000',endColorStr='rgba(0, 0, 0, 0)');
}
.editorial-authors .widget .editorial-avatar-wrap .editorial-info .editorial-title {
    font-size: 22px;
    color: #fff;
    font-weight: 700;
    margin: 0 0 2px;
}
.editorial-authors .widget .editorial-avatar-wrap .editorial-info .editorial-meta {
    font-size: 14px;
    color: #fff;
    font-weight: 400;
    margin: 0;
    display: block;
}
#editorial-wrap .head-text {
    margin: 15px 0 50px;
}
#editorial-wrap {
    display: none;
    margin-bottom: 50px;
}
 #top-bar{ 
    background-image: url(https://1.bp.blogspot.com/-GUTgXwRhUV0/YGAkDor0AGI/AAAAAAAAKXU/VB_EXXtwjms7kFnwy2JhtdVkwp52clA_gCNcBGAsYHQ/s16000/email-bg.jpg);
    background-size: cover;
    background-position: 50%;
    background-repeat: no-repeat;
    background-attachment: fixed;
    width:100%;
    padding:20px 0 0;
    overflow:hidden;
    margin:0;
    position:relative;
}
 .top-bar-bg{ 
 background-color: rgba(68,63,69,0.9);
 position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}
 #top-bar .container{
    margin:0 auto
}
 .top-bar-nav{
    position:relative;
    float:right;
    display:block
}
 .top-bar-nav .widget > .widget-title{
    display:none
}
 .top-bar-nav ul li{
    float:left
}
 .top-bar-nav ul li > a{
    height:30px;
    display:block;
    color:#fff;
    font-size:16px;
    font-weight:400;
    line-height:30px;
    margin:0 0 0 10px;
    padding:0 5px;
    transition:color .17s
}
 .top-bar-nav ul li:first-child > a{
    padding:0 5px 0 0
}
 .top-bar-nav ul > li:hover > a{
    color:$(title.color)
}
 .top-bar-social{
    float:left;
width:66.66666667%;
    position:relative;
    display:block;
margin: 0;
padding:25px 0 0;
text-align:center;
}
#author-email-pic {
    width: 33.33333333%;
    float: right;
}
#author-email-pic img {
    height: auto;
    max-width: 100%;
    vertical-align: top;
}
.top-bar-social .BlogSearch {
    max-width: 550px;
    width: 100%;
    margin: 0 auto;
    overflow:hidden;
}
.top-bar-social .BlogSearch .widget-content {
   background:transparent;
}
.top-bar-social .BlogSearch .search-input {
border-radius: 10px;
    height: auto;
    font-size: 15px;
    width: 100%;
    color: #888;
    font-family: inherit;
    text-align: center;
    padding: 0;
    margin: 0 0 10px;
    box-sizing: border-box;
    border: 1px solid #eaeaea;
    transition: ease .17s;
    line-height: 50px;
}
.top-bar-social .BlogSearch .search-input input {
    width: 100%;
    box-sizing: border-box;
    padding: 0 10px;
    border: 0;
    text-align:center;
    height: 50px;
    border-radius: 10px;
    line-height: 50px;
}
.top-bar-social .BlogSearch .search-action {
width:auto;
display: inline-block;
    padding: 0 15px;
    box-sizing: border-box;
    border-radius: 30px;
background:$(main.color);
    height: 45px;
    font-size: 14px;
    letter-spacing: 1px;
        float: none;
}
.top-bar-social .LinkList {
margin-top:15px;
}
.top-bar-social .widget-title > h3 {
    display: block;
    color: #f2f2f2;
   font-size: 36px;
    line-height: 42px;
    font-weight: 700;
    margin: 0 0 15px;
}
 .top-bar-social .LinkList >  .widget-title{
    display:none
}
 .top-bar-social ul > li{
    display:inline-block
}
 .top-bar-social ul > li > a{
   display:block;
width:30px;
height:30px;
border:1px solid rgba(158, 158, 158, 0.15);
color:#ffffff;
font-size:14px;
text-align:center;
line-height:30px;
padding:0;
margin:0 0 0 10px;
transition:all .17s ease;
border-radius:50%;
}
 
 .top-bar-social ul > li:hover > a{
    color:$(main.color)
}
 .social a:before{
    display:inline-block;
    font-family:FontAwesome;
    font-style:normal;
    font-weight:400
}
 .social .facebook a:before{
    content:"\f230"
}
 .social .facebook-f a:before{
    content:"\f09a"
}
 .social .twitter a:before{
    content:"\f099"
}
 .social .gplus a:before{
    content:"\f0d5"
}
 .social .rss a:before{
    content:"\f09e"
}
 .social .youtube a:before{
    content:"\f16a"
}
 .social .skype a:before{
    content:"\f17e"
}
 .social .stumbleupon a:before{
    content:"\f1a4"
}
 .social .tumblr a:before{
    content:"\f173"
}
 .social .vk a:before{
    content:"\f189"
}
 .social .stack-overflow a:before{
    content:"\f16c"
}
 .social .github a:before{
    content:"\f09b"
}
 .social .linkedin a:before{
    content:"\f0e1"
}
 .social .dribbble a:before{
    content:"\f17d"
}
 .social .soundcloud a:before{
    content:"\f1be"
}
 .social .behance a:before{
    content:"\f1b4"
}
 .social .digg a:before{
    content:"\f1a6"
}
 .social .instagram a:before{
    content:"\f16d"
}
 .social .pinterest a:before{
    content:"\f0d2"
}
 .social .pinterest-p a:before{
    content:"\f231"
}
 .social .twitch a:before{
    content:"\f1e8"
}
 .social .delicious a:before{
    content:"\f1a5"
}
 .social .codepen a:before{
    content:"\f1cb"
}
 .social .reddit a:before{
    content:"\f1a1"
}
 .social .whatsapp a:before{
    content:"\f232"
}
 .social .snapchat a:before{
    content:"\f2ac"
}
 .social .email a:before{
    content:"\f0e0"
}
 .social .external-link a:before{
    content:"\f14c"
}
 .social-color .facebook a,.social-color .facebook-f a{
    background-color:#3b5999
}
 .social-color .twitter a{
    background-color:#00acee
}
 .social-color .gplus a{
    background-color:#db4a39
}
 .social-color .youtube a{
    background-color:#db4a39
}
 .social-color .instagram a{
    background-color:#bc3490
}
 .social-color .pinterest a,.social-color .pinterest-p a{
    background-color:#ca2127
}
 .social-color .dribbble a{
    background-color:#ea4c89
}
 .social-color .linkedin a{
    background-color:#0077b5
}
 .social-color .tumblr a{
    background-color:#365069
}
 .social-color .twitch a{
    background-color:#6441a5
}
 .social-color .rss a{
    background-color:#ffc200
}
 .social-color .skype a{
    background-color:#00aff0
}
 .social-color .stumbleupon a{
    background-color:#eb4823
}
 .social-color .vk a{
    background-color:#4a76a8
}
 .social-color .stack-overflow a{
    background-color:#f48024
}
 .social-color .github a{
    background-color:#24292e
}
 .social-color .soundcloud a{
    background-color:#ff5400
}
 .social-color .behance a{
    background-color:#191919
}
 .social-color .digg a{
    background-color:#1b1a19
}
 .social-color .delicious a{
    background-color:#0076e8
}
 .social-color .codepen a{
    background-color:#000
}
 .social-color .reddit a{
    background-color:#ff4500
}
 .social-color .whatsapp a{
    background-color:#3fbb50
}
 .social-color .snapchat a{
    background-color:#ffe700
}
 .social-color .email a{
    background-color:#888
}
 .social-color .external-link a{
    background-color:$(dark.color)
}
.home #header-wrap {
position:fixed;
}
 #header-wrap{
   position: relative;
    left: 0;
    top: 0;
    padding: 0;
    width: 100%;
    height: 80px;
    z-index: 1010;
    background:#ffffff;
}
 #header-wrap .container{
    margin:0 auto;
    position:relative
}
 .header-logo{
    position:relative;
    float:left;
    height:68px;
    margin:6px 0px;
}
 .header-logo .header-brand{
    display:inline-block;
    line-height:0
}
 .header-logo img{
    max-width:100%;
    height:58px;
    vertical-align:middle
}
 .header-logo h1{
    color:$(main.menu.color);
    font-size:20px;
    line-height:38px;
    margin:0
}
 .header-logo p{
    font-size:12px;
    margin:5px 0 0
}
#parallax-menu {
    display: none;
}
.home #parallax-menu {
    display: block;
}
.scrolling-menu {
    float: right;
    position: relative;
    height: 80px;
}
.scrolling-menu ul > li {
    float: left;
    position: relative;
    margin: 0;
    padding: 0;
    transition: color .17s;
}
.scrolling-menu ul > li > a {
    position: relative;
    color: $(main.menu.color);
    font-size: 16px;
    font-weight: 600;
    text-transform:capitalize;
    line-height: 80px;
    display: inline-block;
    text-decoration: none;
    padding: 0 10px;
    margin: 0 0 0 5px;
    transition: color .17s;
}
.home #main-menu {
display:none;
}
 #main-menu{
    float:right
}
 #main-menu .widget,#main-menu .widget > .widget-title{
    display:none
}
 #main-menu .show-menu{
    display:block
}
 #main-menu{
    position:relative;
    height:68px;
    z-index:15
}
 #main-menu ul > li{
    float:left;
    position:relative;
    margin:0;
    padding:0;
    transition:color .17s
}
 #main-menu ul > li > a{
    position:relative;
    color:$(main.menu.color);
    font-size:12px;
    font-weight:600;
    text-transform:uppercase;
    line-height:68px;
    display:inline-block;
    text-decoration:none;
    padding:0 10px;
    margin:0 0 0 5px;
    transition:color .17s
}
 #main-menu #main-menu-nav > li:last-child > a{
    padding:0 0 0 10px
}
 #main-menu ul > li > a:hover{
    color:$(main.color)
}
 #main-menu ul > li > ul{
    position:absolute;
    float:left;
    left:0;
    top:68px;
    width:180px;
    background-color:$(dark.color);
    z-index:99999;
    margin-top:0;
    padding:0;
    visibility:hidden;
    opacity:0
}
 #main-menu ul > li > ul > li > ul{
    position:absolute;
    float:left;
    top:0;
    left:100%;
    margin-left:0
}
 #main-menu ul > li > ul > li{
    display:block;
    float:none;
    position:relative;
    transition:background .17s ease
}
 #main-menu ul > li > ul > li a{
    display:block;
    height:34px;
    font-size:11px;
    color:#ffffff;
    line-height:34px;
    box-sizing:border-box;
    padding:0 15px;
    margin:0
}

 #main-menu ul > li > ul > li:hover{
    background-color:$(main.color)
}
 #main-menu ul > li > ul > li:hover > a{
    color:#fff
}
 #main-menu ul > li.has-sub > a:after{
    content:'\f107';
    float:right;
    font-family:FontAwesome;
    font-size:12px;
    font-weight:400;
    margin:0 0 0 6px
}
 #main-menu ul > li > ul > li.has-sub > a:after{
    content:'\f105';
    float:right;
    margin:0
}
 #main-menu ul > li:hover > ul,#main-menu ul > li > ul > li:hover > ul{
    visibility:visible;
    opacity:1
}
 #main-menu ul ul{
    transition:all .17s ease
}
 .mobile-menu-toggle, .scrolling-mobile-menu-toggle{
    display:none;
    position:absolute;
    right:0;
    top:0;
    height:80px;
    line-height:80px;
    z-index:20;
    color:$(main.menu.color);
    font-size:21px;
    font-weight:400;
    text-align:left;
    cursor:pointer;
    padding:0 0 0 20px;
    transition:color .17s ease
}
.home .mobile-menu-toggle, .home .mobile-menu-wrap {
display:none;
}
 .mobile-menu-toggle:before, .scrolling-mobile-menu-toggle:before{
    content:"\f0c9";
    font-family:FontAwesome
}
 .nav-active .mobile-menu-toggle:before, .scrolling-active .scrolling-mobile-menu-toggle:before{
    content:"\f00d";
    font-family:FontAwesome
}
 .mobile-menu-toggle:hover, .scrolling-mobile-menu-toggle:hover{
    color:$(main.color)
}
 .overlay{
    display:none;
    position:fixed;
    top:0;
    left:0;
    right:0;
    bottom:0;
    z-index:990;
    background:rgba(0,0,0,0.8)
}
 .mobile-menu-wrap, .scrolling-mobile-menu-wrap{
    display:none
}
 .mobile-menu, .scrolling-mobile-menu{
    position:absolute;
    top:80px;
    left:0;
    width:100%;
    background-color:$(dark.color);
    box-sizing:border-box;
    visibility:hidden;
    z-index:1000;
    opacity:0;
    border-top:1px solid rgba(255,255,255,0.05);
    transition:all .17s ease
}
 .nav-active .mobile-menu, .scrolling-active .scrolling-mobile-menu{
    visibility:visible;
    opacity:1
}
 .mobile-menu > ul, .scrolling-mobile-menu > ul{
    margin:0
}
 .mobile-menu .m-sub{
    display:none;
    padding:0
}
 .mobile-menu ul li, .scrolling-mobile-menu ul li{
    position:relative;
    display:block;
    overflow:hidden;
    float:left;
    width:100%;
    font-size:11px;
    font-weight:600;
    text-transform:uppercase;
    line-height:38px;
    border-bottom:1px solid rgba(255,255,255,0.05)
}
 .mobile-menu ul li:last-child, .scrolling-mobile-menu ul li:last-child{
    border-bottom:0
}
 .mobile-menu > ul li ul{
    overflow:hidden
}
 .mobile-menu ul li a, .scrolling-mobile-menu ul li a{
    color:#ffffff;
    padding:0 20px;
    display:block;
    transition:all .17s ease
}
 .mobile-menu ul li a:hover, .scrolling-mobile-menu ul li a:hover{
    color:$(title.color)
}
 .mobile-menu ul li.has-sub .submenu-toggle{
    position:absolute;
    top:0;
    right:0;
    color:#ffffff;
    cursor:pointer;
    border-left:1px solid rgba(255,255,255,0.05)
}
 .mobile-menu ul li.has-sub .submenu-toggle:after{
    content:'\f105';
    font-family:FontAwesome;
    font-weight:400;
    float:right;
    width:38px;
    font-size:16px;
    line-height:38px;
    text-align:center;
    transition:all .17s ease
}
 .mobile-menu ul li.has-sub .submenu-toggle:hover{
    color:$(main.color)
}
 .mobile-menu ul li.has-sub.show > .submenu-toggle:after{
    transform:rotate(90deg)
}
 .mobile-menu ul li ul li:first-child{
    border-top:1px solid rgba(255,255,255,0.05)
}
 .mobile-menu ul li ul li{
    background-color:rgba(255,255,255,0.05)
}
 #intro-wrap{
    overflow:hidden;
    display:none;
    position:relative;
    float:left;
    width:100%;
    z-index:2;
    margin:0
}
.slide-in {
    font-size: 3.3rem;
    position: absolute;
    bottom: 2.2rem;
    left: 0;
    display: block;
    width: 100%;
    margin: 0;
    padding: 0
}
.slide-in .pointer {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 26px;
    height: 42px;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    border: 2px solid $(main.color);
    border-radius: 26px;
    -webkit-backface-visibility: hidden
}
.slide-in .pointer:after {
    position: absolute;
    top: 5px;
    left: 50%;
    width: 4px;
    height: 4px;
    margin-left: -2px;
    content: '';
    -webkit-transform: translateY(0) scaleY(1) scaleX(1) translateZ(0);
    transform: translateY(0) scaleY(1) scaleX(1) translateZ(0);
    -webkit-animation: scroll 1.5s -1s cubic-bezier(.68, -.55, .265, 1.55) infinite;
    animation: scroll 1.5s -1s cubic-bezier(.68, -.55, .265, 1.55) infinite;
    opacity: 1;
    border-radius: 100%;
    background-color: $(main.color)
}
@-webkit-keyframes scroll {
    0%, 20% {
        -webkit-transform: translateY(0) scaleY(1) scaleX(1) translateZ(0);
        transform: translateY(0) scaleY(1) scaleX(1) translateZ(0)
    }
    10% {
        -webkit-transform: translateY(0) scaleY(1.2) scaleX(1.2) translateZ(0);
        transform: translateY(0) scaleY(1.2) scaleX(1.2) translateZ(0);
        opacity: 1
    }
    to {
        -webkit-transform: translateY(20px) scaleY(2.5) scaleX(.5) translateZ(0);
        transform: translateY(20px) scaleY(2.5) scaleX(.5) translateZ(0);
        opacity: .01
    }
}
@keyframes scroll {
    0%, 20% {
        -webkit-transform: translateY(0) scaleY(1) scaleX(1) translateZ(0);
        transform: translateY(0) scaleY(1) scaleX(1) translateZ(0)
    }
    10% {
        -webkit-transform: translateY(0) scaleY(1.2) scaleX(1.2) translateZ(0);
        transform: translateY(0) scaleY(1.2) scaleX(1.2) translateZ(0);
        opacity: 1
    }
    to {
        -webkit-transform: translateY(20px) scaleY(2.5) scaleX(.5) translateZ(0);
        transform: translateY(20px) scaleY(2.5) scaleX(.5) translateZ(0);
        opacity: .01
    }
}
@-webkit-keyframes blink {
    0%, to {
        opacity: 1
    }
    50% {
        opacity: 0
    }
}
@keyframes blink {
    0%, to {
        opacity: 1
    }
    50% {
        opacity: 0
    }
}
 #main-intro{
    position:relative;
    float:left;
    width:100%;
    height:100%;
   background:#ffffff;
background-blend-mode: overlay, screen, overlay, overlay, normal;
}
 #main-intro .widget{
    height:100%;
    width:100%;
    display:block;
    overflow:hidden
}
#main-intro .widget-content {
    margin:0 -15px;   
    align-items: center;
}
#main-intro .container {
    height: auto!important;
    margin: 0 auto;
}
#main-intro .widget.HTML, #main-intro #HTML100 {
    display: none;
}
 .intro-content{
    z-index: 10!important;
    position: absolute;
    top: 50%;
    width:40%;
    left: 50%;
    right: 0;
    margin: auto;
    padding: 25px;
    z-index: 3;
    text-align: center;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}
 .intro-title{
    text-transform: capitalize;
    line-height: 1em;
    font-family: "Damion", Sans-serif;
    font-size:60px;
    color:#fff;
    font-weight:500;
    margin:0 0 20px;
}

 .intro-snippet{
    font-size:18px;
    line-height: 1.5em;
    color:#fff;
    margin:0
}
 .intro-action a, .service-action a{
    position:relative;
    display:inline-block;
    height:40px;
    font-size:14px;  
    overflow: hidden;
    z-index: 1;
    color:#fff;
    line-height:40px;
    padding:0 25px;
    margin:30px 0 0;
    transition:background .17s ease;
}
.intro-action a:after, .service-action a:after {   
    position: absolute;
    left: 0;
    top: 0;
    content: "";
    height: 100%;
    width: 100%;
    background-color:$(main.color);
    transition: all .35s ease-in-out;
    z-index: -1;
}
 .intro-action a:hover{
    background-color:$(dark.color)
}
.intro-image {
    position:relative;
    width:100%;
    text-align: left;
    margin: 0;
}
.intro-image:after {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 4;
    width: 100%;
    height: 100%;
    opacity: .5;
    background-color: transparent;
    background-image: linear-gradient(90deg, rgba(255,255,255,0) 0%, #dfc78f 100%);
    opacity: 0.85;
    content: '';
}
.intro-image img {
width:100%;
}
#brand-services-wrap{
    display:none;
    float:left;
    width:100%;
   background:#001d4c;
    padding:30px 0
}
#brand-services-wrap .container {
    position: relative;
    margin: 0 auto;
}
#brand-services-wrap ul {
padding:0;
margin:0;
list-style:none;
}
#brand-services-wrap li{
float: left;
    width: calc(100% / 5);
    box-sizing: border-box;
    padding: 0 20px;
position:relative;
text-align:center;
}

.head-text{
    float:left;
    width:100%;
    text-align:center;
    margin:50px 0 50px
}

 .head-text .widget-title > h3{
    display:block;
    font-size:36px;
    letter-spacing:0.4px;
    color:$(title.color);
    font-weight:600;
    margin:0 0 20px
}
 .head-text .widget-content{
    font-size:16px;
    line-height: 1.6em;
    margin:0
}
 #intro-author-wrap{
   display:none;
    position:relative;
    float:left;
    width:100%;
    padding:0;
    margin:0;
}
 #intro-author-wrap .container{
    position:relative;
    margin:0 auto
}
#intro-author-heading {
    width: 100%;
    float: left;
    display: block;
    position: relative;
    padding: 80px 0;
    margin-bottom: 30px;
    text-align: center;
    background: linear-gradient(to bottom right,#ff4f9c,#ff4f9c);
}

#intro-author-heading .button {
    margin: 20px 0 0;
    padding: 8px 20px;
    background: $(main.color);
    color:#fff;
display: inline-block;
    float: none;
}
#intro-author-heading .button:hover {
   background: $(main.color);
}
.author-intro-widgets {
}
.author-intro-widgets {
margin: 0;
display: grid;
    grid-template-columns: repeat(3,1fr);
    grid-gap: 15px;
}
.author-intro-widgets .left-side-widget, .author-intro-widgets .center-side-widget, .author-intro-widgets .right-side-widget {
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    padding: 0;
    box-sizing: border-box;
    position:relative;
}
.author-intro-widgets .right-side-widget {

}
.author-intro-widgets .author-list .widget {
    display: block;
    list-style: none;
    width:100%;
    float: left;
    margin:0;
    box-sizing: border-box;
position:relative;
}
 #intro-author-photo{
    position:relative;
    float:left;
    width:100%;
    height:400px;
    box-sizing:border-box;
    padding:0 10px
}
 #intro-author-photo .author-image{
    position:relative;
    display:block;
    width:100%;
    height:100%;
    background-repeat:no-repeat;
    background-size:cover;
    margin:0
}
 
 .author-title{
    display:block;
    font-size:40px;
    color:#ffffff;
    font-weight:700;
    margin:15px 0 20px
}
 .author-snippet{
    font-size:14px;
     color:#f2f2f2;
    line-height:24px;
    margin:0
}
 #intro-services-wrap{
    display:none;
    float:left;
    width:100%;
    background-color:#f8f8f8;
    padding:80px 0
}
 #intro-services-wrap .container{
    position:relative;
    margin:0 auto
}
 #intro-services{
    display:block;
    margin:0 -20px
}
 #intro-services .widget{
    float:left;
    width:calc(100% / 3);
    box-sizing:border-box;
    padding:0 20px
}
 .service-content{
    display:block;
    text-align:center
}
 #intro-services .service-icon{
    display:inline-block;
    width:70px;
    height:70px;
    text-align:center;
    border-radius:100%;
    overflow:hidden
}
 #intro-services .service-icon img{
    display:block;
    width:100%;
    height:100%;
    object-fit:cover;
    border-radius:100%;
    color:transparent
}
 #intro-services .service-icon i{
    display:block;
    background-color:#f2f2f2;
    font-size:30px;
    color:$(main.color);
    line-height:70px;
    font-weight:400;
    font-style:normal;
    margin:0
}


.author-intro-widgets .author-list .service-content-details {
    position: relative;
    z-index: 1;
    background: #fff;
    float: left;
    width: 100%;
    padding: 30px;
    box-shadow: 0 5px 30px 0 rgb(214 215 216 / 57%);
}
.author-intro-widgets .author-list .service-content-details .service-snippet {
    color: $(body.text.color);
    font-size: 13px;
line-height: 22px;
}
.author-intro-widgets .left-side-widget .service-content {
text-align:right;
}
.author-intro-widgets .author-list .service-content {
    text-align: left;
    padding: 0;
    position: relative;
    box-sizing: border-box;
}
.author-intro-widgets .service-content .service-content-box-color {
    float: left;
    width: 100%;
    height: 250px;
    margin: 0;
    position: relative;
    display: block;
    box-sizing: border-box;
}
.author-intro-widgets .service-content .service-content-box-color img {
    display: block;
    position: relative;
    width: 100%;
    height: 100%;
    color: transparent;
    object-fit: cover;
    z-index: 1;
}
 .service-title{
    font-size:14px;
    color:$(dark.color);
    font-weight:700;
    text-transform:capitalize;
    margin:25px 0 15px
}
.author-intro-widgets .author-list  .service-title{
font-size: 28px;
    line-height: 1.2;
margin:0 0 10px;
}
overflow:hidden;
}
 .service-snippet{
    font-size:14px;
    line-height:24px;
    margin:0
}
 .head-text{
    float:left;
    width:100%;
    text-align:center;
    margin:50px 0 50px
}

.counter-boxs .container {
    padding-bottom: 30px;
    padding-top: 30px;
    margin: 0 auto;
}
.counter-boxs {
    background: #fff;
}
#info-tile .widget-title > h3{
    color: #000;
    font-size: 32px;
    letter-spacing: 0px;
    display: block;
    text-align: center;
    font-weight: 500;
    margin: 0 0 15px 0;
}
#info-tile .widget-content {
    margin-bottom: 30px;
    text-align: center;
    color: #666666;
    font-size: 16px;
    line-height: 1.5em;
}

.fa {
    margin-right: 10px;
    padding: 5px;
    color: #fff;
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: 18px;
    background: #dfc78f;
}


.project-head {
margin:0 auto;
}
 #serv-tile-wrap{
    background:#f2f2f2;
    display:none;
    float:left;
    width:100%;
    margin:0;
    padding: 40px 0;
}
 #serv-tile-wrap .container{
    position:relative;
    margin:0 auto
}
 #serv-tile{
}
 #serv-tile .widget{
    border-radius:6px;
    border: 1px solid #ff4081;
    flex-direction: column;
    width: 31.5%;
    padding: 30px;
    box-sizing: border-box;
    text-align: center;
    box-shadow: 0 5px 30px 0 rgb(214 215 216 / 57%);
    background: #fff;
    float: left;
    margin: 0 25px 25px 0px;
}
#serv-tile .widget:nth-child(4n) {
margin-right: 35px;
width: 48%;
}
#serv-tile .widget:nth-child(3n) {
margin-right:0px;
}
#serv-tile .widget:nth-child(5n) {
    margin-right: 0px;
    width: 48%;
}
h3.serv-tile-box-title {
    font-size: 21px;
    color: #111;
    margin: 5px 0 0px 0;
    font-weight: 500;
}
p.serv-tile-box-meta {
    line-height: 1.4em;
    color: #666;
}
.serv-tile-box-avatar {
    display: inline-block;
    position: relative;
    text-align: center;
    margin: 0 auto 0px;
    color:$(main.color);
    font-size: 50px;
}
.serv-tile-box-avatar {
    
}
.serv-tile-box-avatar i {
    color: #ff4f9c;
    background: #fff;
    display: block;
    font-size: 48px;
    font-weight: 400;
    font-style: normal;
    margin: 0;
}
 .serv-tile-box-avatar img{
   width: 80px;
    height: 80px;
}
 .serv-tile-box-info{
margin-top: 25px;
    overflow:hidden
}
 .serv-tile-box-title{
    font-size: 24px;
    color:$(dark.color.two);
    font-weight:700;
    margin:0 0 7px
}
 .serv-tile-box-meta{
    font-size:16px;
line-height: 1.75;
    color:$(body.text.two)
}
#menu-bar-nav li {
    text-align:left;
    position: relative;
    display: block;
    padding: 7px 0;
}
#menu-bar-nav li a{
    display: block;
    color: #041a57;
    font-size: 14px;
    font-weight: 500;
    text-transform: capitalize;
    transition: color .17s;
}
#menu-bar-nav li span {
    display: block;
    position: relative;
    float: right;
    width: 25px;
    height: 25px;
    background-color: #ff4f9c;
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    line-height: 25px;
}



.owl-carousel{
display:none;
width:100%;
-webkit-tap-highlight-color:transparent;
position:relative;
z-index:1
}
.owl-nav {
}
.owl-carousel .owl-stage{
position:relative;
-ms-touch-action:pan-Y
}
.owl-carousel .owl-stage:after{
content:".";
display:block;
clear:both;
visibility:hidden;
line-height:0;
height:0
}
.owl-carousel .owl-stage-outer{
position:relative;
overflow:hidden;
-webkit-transform:translate3d(0px,0px,0px)
}
.owl-carousel .owl-controls .owl-nav .owl-prev,.owl-carousel .owl-controls .owl-nav .owl-next,.owl-carousel .owl-controls .owl-dot{
cursor:pointer;
cursor:hand;
-webkit-user-select:none;
-khtml-user-select:none;
-moz-user-select:none;
-ms-user-select:none;
user-select:none
}
.owl-carousel.owl-hidden{
opacity:0
}
.owl-carousel .owl-refresh .owl-item{
display:none
}
.owl-carousel.owl-loading{
opacity:0;
display:block
}
.owl-carousel.owl-loaded{
display:block
}
.owl-carousel .owl-item{
position:relative;
min-height:1px;
float:left;
-webkit-backface-visibility:visible;
-webkit-tap-highlight-color:transparent;
-webkit-touch-callout:none;
-webkit-user-select:none;
-moz-user-select:none;
-ms-user-select:none;
user-select:none
}
.owl-carousel .owl-item img, ul.customer-logos img{
display:block;
}

.owl-carousel.owl-text-select-on .owl-item{
-webkit-user-select:auto;
-moz-user-select:auto;
-ms-user-select:auto;
user-select:auto
}
.owl-carousel .owl-grab{
cursor:move;
cursor:-webkit-grab;
cursor:-o-grab;
cursor:-ms-grab;
cursor:grab
}
.owl-carousel.owl-rtl{
direction:rtl
}
.owl-carousel.owl-rtl .owl-item{
float:right
}
.no-js .owl-carousel{
display:block
}
.owl-carousel .animated{
-webkit-animation-duration:1000ms;
animation-duration:1000ms;
-webkit-animation-fill-mode:both;
animation-fill-mode:both
}
.owl-carousel .owl-animated-in{
z-index:1
}
.owl-carousel .owl-animated-out{
z-index:0
}
.owl-carousel .owl-item li {
padding: 0;
}
.owl-height{
-webkit-transition:height 500ms ease-in-out;
-moz-transition:height 500ms ease-in-out;
-ms-transition:height 500ms ease-in-out;
-o-transition:height 500ms ease-in-out;
transition:height 500ms ease-in-out
}
.owl-carousel .owl-controls .owl-dots {
    margin-top: 25px;
    text-align: center;
}
.owl-carousel .owl-controls .owl-dot {
    display: inline-block;
}
.owl-carousel .owl-controls .owl-dots span {
    background: none repeat scroll 0 0 #869791;
    border-radius: 20px;
    display: block;
    height: 12px;
    margin: 5px 7px;
    opacity: 0.5;
    width: 12px;
}
.owl-carousel .owl-controls .owl-dot.active span {
background:none repeat scroll 0 0 $(main.color);
}
.owl-prev,.owl-next{
position:relative;
float:left;
width:24px;
height:24px;
background-color:#fff;
font-family: FontAwesome;
text-rendering: auto;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
font-size:11px;
line-height:23px;
font-weight:900;
color:#bdbdbd;
text-align:center;
cursor:pointer;
border:1px solid rgba(0,0,0,0.08);
box-sizing:border-box;
transition:all .25s ease
}
.owl-prev:before{
content:"\f053"
}
.owl-next:before{
content:"\f054"
}
.owl-prev:hover,.owl-next:hover{
background-color:$(main.color);
color:#fff;
border-color:$(main.color)
}
@keyframes fadeInLeft {
     from{
        opacity:0;
        transform:translate3d(-30px,0,0)
    }
     to{
        opacity:1;
        transform:none
    }
}
 @keyframes fadeOutLeft {
     from{
        opacity:1
    }
     to{
        opacity:0;
        transform:translate3d(-30px,0,0)
    }
}
 @keyframes fadeInRight {
     from{
        opacity:0;
        transform:translate3d(30px,0,0)
    }
     to{
        opacity:1;
        transform:none
    }
}
 .fadeInRight{
    animation-name:fadeInRight
}
 @keyframes fadeOutRight {
     from{
        opacity:1
    }
     to{
        opacity:0;
        transform:translate3d(30px,0,0)
    }
}
 .fadeOutRight{
    animation-name:fadeOutRight
}
 #testimonial-wrap{
    display:none;
    float:left;
    width:100%;
    margin:0 0 75px
}
 #testimonial-wrap .container{
    position:relative;
    margin:0 auto
}
 #testimonial{
    float:left;
    width:100%;
    margin:0
}
 #testimonial .widget{
  position: relative;
    float: left;
    width: 100%;
overflow: hidden;
    padding: 15px;
    box-sizing: border-box;
}
 #testimonial .widget:nth-child(2), #testimonial .widget:nth-child(4){
}
 .testi-avatar{
    float:left;
    width:45px;
    height:45px;
    overflow:hidden;
border-radius:50%;
    margin:0 15px 0 0
}
 .testi-avatar img{
    display:block;
    width:100%;
    height:100%;
    object-fit:cover;
    color:transparent;
    margin:0
}
 .testi-info{
    overflow:hidden
}
 .testi-title{
    font-size:20px;
    color:#443f45;
    font-weight:700;
    margin:0 0 3px
}
 .testi-meta{
    font-size:12px;
    color:#999999
}
.testi-info-quotes {
    margin-bottom: 26px;
}
 .testi-snippet{
       font-size: 15px;
    line-height: 1.5625;
    color: #6a6a6a;
    padding: 15px 0;
    margin:0;
    font-style: italic;
    font-family: cursive;
    letter-spacing: 0.5px;
}
.testi-snippet:before {
    content: '\f10d';
    display: inline-block;
    font-family: FontAwesome;
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin-right: 10px;
    color:$(main.color);
} 
.testi-snippet:after {
    content: '\f10e';
    display: inline-block;
    font-family: FontAwesome;
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin-left: 10px;
   color:$(main.color);
}
#testimonial .widget:nth-child(2) .testi-snippet, #testimonial .widget:nth-child(4) .testi-snippet {
    background-color: #e22310;
    border-color: #d41f0d;
}


 .main .widget{
    position:relative
}
 .queryMessage{
    overflow:hidden;
    color:$(title.color);
    font-size:13px;
    font-weight:400;
   padding: 10px;
    margin: 0 0 25px;
    background-color: #eee;
    border: 1px solid #ccc;
    box-sizing: border-box;
}
 .queryMessage .search-query,.queryMessage .search-label{
    font-weight:600;
    text-transform:uppercase
}
 .queryMessage .search-query:before,.queryMessage .search-label:before{
    content:"\201c"
}
 .queryMessage .search-query:after,.queryMessage .search-label:after{
    content:"\201d"
}
 .queryMessage a.show-more{
float: right;
    display:inline-block;
    color:$(main.color);
    text-decoration:underline;
    margin:0 0 0 10px
}
 .queryEmpty{
    font-size:13px;
    font-weight:400;
    padding:10px 0;
    margin:0 0 25px;
    text-align:center
}
 .blog-post{
    display:block;
    overflow:hidden;
    word-wrap:break-word
}
.index-post-wrap {
    position: relative;
float: left;
    width: 100%;
}
.grid-posts {
  display: grid;
    grid-template-columns: repeat(3,1fr);
    grid-gap: 30px;
    margin: 0;
}
 .index-post{
    display: flex;
    flex-direction: column;
    width: 100%;
    box-shadow: 0 5px 30px 0 rgb(214 215 216 / 57%);
    border-radius: 10px;
    background: #fff;
    box-sizing: border-box;
    padding:15px;
    margin:0;
    overflow: visible;
}
 .index-post .post-image-wrap{
  float: left;
    width: 100%;
    height: 200px;
    margin: 0;    
    border-radius: 10px;
    overflow: hidden;
}
 .index-post .post-image-wrap .post-image-link{
    width:100%;
    height:100%;
    position:relative;
    display:block;
    z-index:1;
    -webkit-border-radius: 4px 4px 0 0;
    -khtml-border-radius: 4px 4px 0 0;
    -moz-border-radius: 4px 4px 0 0;
    -ms-border-radius: 4px 4px 0 0;
    -o-border-radius: 4px 4px 0 0;
    border-radius: 4px 4px 0 0;
    overflow:hidden
}
 .index-post .post-content{
    margin:0;
   float: left;
    width: 100%;
}
 .post-image-wrap:hover .post-content{
  
}
 .index-post .post-info{
    display: block;
    float:left;
    width:100%;
    padding: 15px 30px 35px;
    box-sizing: border-box;
    min-height: 200px;
}
 .index-post .post-info > h2{
    font-size:24px;
    color:$(title.color);
    font-weight:600;
    line-height:1.5em;
    margin:0 0 10px
}
 .index-post .post-info > h2 a{
color:$(title.color);
}
 .post-meta{
    color:#989898;
    font-weight:400;
    font-size: 11px;
    text-transform:  capitalize;
    padding:0;
}
.index-post .post-meta .post-author{
 
}
.index-post .post-meta .post-author:before {

}
 .post-meta .post-date{
    display:inline-block;
    margin:0 7px 0 0
}
.post-meta .post-author, .post-meta .post-date {
    float: left;
    display: inline-block;
    margin: 0 10px 0 0;
}
.post-meta .post-author:before, .post-meta .post-date:before, .post-meta .post-tag:before {
    font-family: FontAwesome;
    font-weight: 400;
    margin: 0 3px 0 0;
}
.post-meta .post-author:before {
    content: '\f007';
}
.post-meta .post-date:before {
    content: '\f017';
}
.post-meta .post-tag:before {
    content: '\f022';
}
.post-meta a {
    color: #989898;
    transition: color .17s;
}
.post-snippet {
    position: relative;
    display: block;
    overflow: hidden;
    font-size: 14px;
    line-height: 1.6em;
    font-weight: 400;
    margin: 7px 0 0;
color: #5a5a5a;
}
 .widget iframe,.widget img{
    max-width:100%
}
 .item-post h1.post-title{
    font-size:27px;
    color:$(title.color);
    line-height:1.5em;
    font-weight:700;
    position:relative;
    display:block;
    margin:0 0 15px
}
 .static_page .item-post h1.post-title{
    margin:0
}
 .item-post .post-header .post-meta{
    font-size:13px
}
 .item-post .post-body{
    display:block;
    font-size:14px;
    line-height:1.6em;
    padding:35px 0 0
}
 .static_page .item-post .post-body{
    padding:35px 0
}
 .item-post .post-outer{
    padding:0
}
 .item-post .post-body img{
    max-width:100%
}
 .post-footer{
    position:relative;
    float:left;
    width:100%;
    margin:35px 0 40px
}
 .post-labels{
    float:left;
    height:auto;
    position:relative
}
 .post-labels a{
    float:left;
    height:26px;
    background-color:#f9f9f9;
    color:#aaa;
    font-size:11px;
    font-weight:600;
    text-transform:uppercase;
    line-height:26px;
    padding:0 10px;
    margin:0 10px 0 0;
    border-radius:3px;
    transition:all .17s ease
}
 .post-labels a:hover{
    background-color:$(main.color);
    color:#fff
}
 .post-share{
    position:relative;
    float:right;
    overflow:hidden;
    line-height:0
}
 ul.share-links{
    position:relative
}
 .share-links li{
    float:left;
    box-sizing:border-box;
    margin:0 0 0 5px
}
 .share-links li.whatsapp-mobile{
    display:none
}
 .is-mobile li.whatsapp-desktop{
    display:none
}
 .is-mobile li.whatsapp-mobile{
    display:inline-block
}
 .share-links li a{
    float:left;
    display:inline-block;
    width:35px;
    height:26px;
    color:#fff;
    font-size:12px;
    text-align:center;
    line-height:26px;
    border-radius:3px;
    transition:all .17s ease
}
 .share-links li a:before{
    font-size:14px
}
 .share-links li a:hover{
    background-color:$(main.color);
    color:#fff
}
#related-wrap{
    margin: 20px 0 30px;
    overflow: hidden;
    float: left;
    width: 100%;
}
#related-wrap .title-wrap {
    position: relative;
    float: left;
    width: 100%;
    height: 28px;
    background-color: $(main.color);
    display: block;
    margin: 0 0 20px;
}
#related-wrap .title-wrap > h3 {
    display: block;
    font-size: 12px;
    color: #ffffff;
    font-weight: 600;
    line-height: 28px;
    text-transform: uppercase;
    text-align: center;
    padding: 0 15px;
    margin: 0;
}
 #related-wrap .related-tag{
    display:none
}
 .related-ready{
    float:left;
    width:100%
}
 .related-ready .loader{
    height:178px
}
 ul.related-posts{
    position:relative;
    overflow:hidden;
    margin:0 -10px;
    padding:0
}
 .related-posts .related-item{
    width:33.33333333%;
    position:relative;
    overflow:hidden;
    float:left;
    display:block;
    box-sizing:border-box;
    padding:0 10px;
    margin:0
}
 .related-posts .post-image-link{
    width:100%;
    height:130px;
    position:relative;
    overflow:hidden;
    display:block
}
 .related-posts .post-title{
    font-size:14px;
    font-weight:600;
    line-height:1.4em;
    display:block;
    margin:7px 0 5px
}
 .related-posts .post-title a{
    color:#303030;
    transition:color .17s
}
 .related-posts .related-item:hover .post-title a{
    color:$(main.color)
}
 .related-posts .post-meta{
    font-size:12px
}

 .post-nav{
    position:relative;
    overflow:hidden;
    display:block;
    margin:0
}
 .post-nav .nav-link{
    display:block;
    height:30px;
    background-color:#f9f9f9;
    font-size:11px;
    color:#aaa;
    line-height:30px;
    text-transform:uppercase;
    font-weight:600;
    padding:0 25px;
    border-radius:3px;
    transition:all .17s ease
}
 .post-nav .nav-link:hover{
    background-color:$(main.color);
    color:#fff
}
 .post-nav span.nav-link:hover{
    background-color:#f8f8f8;
    color:#999
}
 .next-post-link{
    float:left
}
 .prev-post-link{
    float:right
}
 .next-post-link:after{
    content:"\f104";
    float:left;
    font-family:FontAwesome;
    font-size:13px;
    font-weight:400;
    text-transform:none;
    margin:0 3px 0 0
}
 .prev-post-link:before{
    content:"\f105";
    float:right;
    font-family:FontAwesome;
    font-size:13px;
    font-weight:400;
    text-transform:none;
    margin:0 0 0 3px
}
 #blog-pager{
    float:left;
    width:100%;
    text-align:center;
    margin:16px 0 0
}
 .blog-pager a{
    display:inline-block;
    height:32px;
    background-color:$(main.color);
    color:#fff;
    font-size:14px;
    font-weight:400;
    line-height:32px;
    text-transform:capitalize;
    text-align:center;
    padding:0 20px;
    border-radius:3px;
    transition:all .17s ease
}
 .blog-pager a:hover{
    background-color:$(dark.color)
}
 .blog-post-comments{
    display:none;
    overflow:hidden;
    background-color:#f8f8f8;
    padding:10px 30px;
    margin:0 0 40px
}
 #comments{
    margin:0
}
 #gpluscomments{
    float:left!important;
    width:100%!important;
    margin:0 0 25px!important
}
 #gpluscomments iframe{
    float:left!important;
    width:100%
}
 .comments{
    display:block;
    clear:both;
    margin:0
}
 .comments > h3{
    float:left;
    width:100%;
    font-size:12px;
    font-style:italic;
    font-weight:400;
    margin:0 0 20px
}
 .no-comments > h3{
    margin:10px 0 15px
}
 .comments .comments-content{
    float:left;
    width:100%;
    margin:0
}
 #comments h4#comment-post-message{
    display:none
}
 .comments .comment-block{
    position:relative;
    background-color:#fdfdfd;
    padding:15px;
    margin:0 0 0 55px;
    border:1px solid #f2f2f2
}
 .comments .comment-block:before{
    content:'';
    position:absolute;
    top:8px;
    left:-5px;
    width:0;
    height:0;
    border:5px solid #f2f2f2;
    border-top-color:transparent;
    border-right-color:transparent;
    transform:rotate(45deg)
}
 .comments .comment-content{
    font-size:13px;
    line-height:1.6em;
    margin:10px 0
}
 .comment-thread .comment{
    position:relative;
    padding:10px 0 0;
    margin:10px 0 0;
    list-style:none
}
 .comment-thread ol{
    padding:0;
    margin:0 0 20px
}
 .comment-thread ol > li:first-child{
    padding:0;
    margin:0
}
 .comment-thread .avatar-image-container{
    position:absolute;
    top:10px;
    left:0;
    width:40px;
    height:40px;
    overflow:hidden
}
 .comment-thread ol > li:first-child > .avatar-image-container{
    top:0
}
 .avatar-image-container img{
    width:100%;
    height:100%
}
 .comments .comment-header .user{
    font-size:14px;
    color:$(title.color);
    display:inline-block;
    font-style:normal;
    font-weight:700;
    margin:0
}
 .comments .comment-header .user a{
    color:$(title.color);
    transition:color .17s ease
}
 .comments .comment-header .user a:hover{
    color:$(main.color)
}
 .comments .comment-header .icon.user{
    display:none
}
 .comments .comment-header .icon.blog-author{
    display:inline-block;
    font-size:12px;
    color:$(main.color);
    font-weight:400;
    vertical-align:top;
    margin:0 0 0 5px
}
 .comments .comment-header .icon.blog-author:before{
    content:'\f058';
    font-family:FontAwesome
}
 .comments .comment-header .datetime{
    float:right;
    display:inline-block;
    margin:0
}
 .comment-header .datetime a{
    font-size:12px;
    color:#aaa;
    font-style:italic
}
 .comments .comment-actions{
    display:block;
    margin:0
}
 .comments .comment-actions a{
    color:#aaa;
    font-size:11px;
    font-style:italic;
    margin:0 15px 0 0;
    transition:color .17s ease
}
 .comments .comment-actions a:hover{
    color:$(main.color);
    text-decoration:underline
}
 .loadmore.loaded a{
    display:inline-block;
    border-bottom:1px solid rgba(0,0,0,0.1);
    text-decoration:none;
    margin-top:15px
}
 .comments .continue{
    display:none!important
}
 .comments .comment-replies{
    padding:0 0 0 55px
}
 .thread-expanded .thread-count a,.loadmore{
    display:none
}
 .comments .footer,.comments .comment-footer{
    font-size:13px
}
 .comment-form{
    margin:0 -7.5px
}
 .comment-form > p{
    font-size:13px;
    padding:10px 0 5px
}
 .comment-form > p > a{
    color:$(title.color)
}
 .comment-form > p > a:hover{
    text-decoration:underline
}
 .post-body h1,.post-body h2,.post-body h3,.post-body h4,.post-body h5,.post-body h6{
    color:$(title.color);
    font-weight:700;
    margin:0 0 15px
}
 .post-body h1,.post-body h2{
    font-size:24px
}
 .post-body h3{
    font-size:21px
}
 .post-body h4{
    font-size:18px
}
 .post-body h5{
    font-size:16px
}
 .post-body h6{
    font-size:13px
}
 blockquote{
    background-color:#f8f8f8;
    font-style:italic;
    padding:10px 15px;
    margin:0;
    border-left:3px solid $(main.color)
}
 blockquote:before,blockquote:after{
    display:inline-block;
    font-family:FontAwesome;
    font-weight:400;
    font-style:normal;
    line-height:1
}
 blockquote:before{
    content:'\f10d';
    margin:0 10px 0 0
}
 blockquote:after{
    content:'\f10e';
    margin:0 0 0 10px
}
 .widget .post-body ul,.widget .post-body ol{
    line-height:1.5;
    font-weight:400
}
 .widget .post-body li{
    margin:5px 0;
    padding:0;
    line-height:1.5
}
 .post-body ul{
    padding:0 0 0 20px
}
 .post-body ul li:before{
    content:"\f105";
    font-family:FontAwesome;
    font-size:13px;
    font-weight:900;
    margin:0 5px 0 0
}
 .post-body u{
    text-decoration:underline
}
 .post-body a{
    transition:color .17s ease
}
 .post-body strike{
    text-decoration:line-through
}
 .contact-form-widget form{
    font-weight:400
}
 .contact-form-name,.contact-form-email{
    float:left;
    width:calc(50% - 5px);
    height:34px;
    background-color:rgba(255,255,255,0.01);
    font-family:inherit;
    font-size:13px;
    color:#fff;
    line-height:34px;
    box-sizing:border-box;
    padding:0 10px;
    margin:0 0 10px;
    border:1px solid #ddd;
    border-radius:3px
}
 .contact-form-email{
    float:right
}
 .contact-form-email-message{
    float:left;
    width:100%;
    background-color:rgba(255,255,255,0.01);
    font-family:inherit;
    font-size:13px;
    color:#fff;
    box-sizing:border-box;
    padding:10px;
    margin:0 0 10px;
    border:1px solid #ddd;
    border-radius:3px
}
 .contact-form-button-submit{
    float:left;
    width:100%;
    height:34px;
    background-color:$(main.color);
    font-family:inherit;
    font-size:12px;
    color:#fff;
    line-height:34px;
    font-weight:600;
    text-transform:uppercase;
    cursor:pointer;
    box-sizing:border-box;
    padding:0 10px;
    margin:0;
    border:0;
    border-radius:3px;
    transition:background .17s ease
}
 .contact-form-button-submit:hover{
    background-color:rgba(0,0,0,0.5)
}
 .contact-form-error-message-with-border,.contact-form-success-message-with-border{
    float:left;
    width:100%;
    background-color:#e74c3c;
    color:#f2f2f2;
    font-size:11px;
    text-align:center;
    line-height:11px;
    padding:4px 0;
    margin:10px 0;
    border-radius:3px
}
 .contact-form-success-message-with-border{
    background-color:#3498db
}
 .contact-form-cross{
    margin:0 0 0 3px
}
 .contact-form-error-message,.contact-form-success-message{
    margin:0
}
.map-me {
margin:0 0 -5px;
display: block;
max-width: 100%;
width: 100%;
box-sizing: border-box;
}
.map-me #map iframe {
width: 100%;
height: 378px;
}
 #footer-wrapper{
    position:relative;
    overflow:hidden;
    background:$(dark.color);
    background-size: cover;
    background-position: top;
    margin:0
}
.deva-svg-shape {
    background: #001d4c;
    padding-bottom: 10px;
}
.deva-svg-shape svg {
    fill: #f3f7fd;
    margin-bottom: -10px;
    overflow: hidden;
    vertical-align: middle;
    padding: 0;
}
 #footer-copyright{
    background:$(dark.color);
    border-style: solid;
    border-width: 1px 0 0;
    border-color: #ffffff29;
    display:block;
    overflow:hidden;
    width:100%;
    color:#aaa;
    padding:20px 0
}
 #footer-copyright > .container{
    margin:0 auto
}
 #social-footer{
    float:right
}
 #social-footer .widget{
    line-height:30px
}
 .social-footer ul{
    text-align:center;
    overflow:hidden;
    display:block
}
 .social-footer ul li{
    display:inline-block;
    margin:0 0 0 14px
}
 .social-footer ul li a{
    font-size:16px;
    color:#aaa;
    display:block;
    padding:0 3px;
    transition:color .17s ease
}
 .social-footer ul li:last-child a{
    padding-right:0
}
 .social-footer ul li a:hover{
    color:$(main.color)
}
 #footer-copyright .copyright-area{
    color:#fff;
    float:left;
    font-size:16px;
    line-height:30px;
}
#footer-copyright .copyright-area span {
    color:#fff;
    font-size:14px;
    line-height:30px;
}
 #footer-copyright .copyright-area a{
    color:#ffff00f2;
}
 #footer-copyright .copyright-area a:hover{
    text-decoration:underline
}
.hidden-widgets{
    display:none;
    visibility:hidden
}
 .back-top{
    display:none;
    z-index:1010;
    width:32px;
    height:32px;
    position:fixed;
    bottom:25px;
    right:25px;
    background-color:$(main.color);
    cursor:pointer;
    overflow:hidden;
    font-size:19px;
    color:#fff;
    text-align:center;
    line-height:32px;
    border-radius:3px
}
 .back-top:after{
    content:'\f106';
    position:relative;
    font-family:FontAwesome;
    font-weight:400
}
 .error404 #main-wrapper{
    width:100%!important;
    margin:0!important
}
 .error404 #sidebar-wrapper{
    display:none
}
 .errorWrap{
    color:$(title.color);
    text-align:center;
    padding:60px 0 100px
}
 .errorWrap h3{
    font-size:130px;
    line-height:1;
    margin:0 0 30px
}
 .errorWrap h4{
    font-size:25px;
    margin:0 0 20px
}
 .errorWrap p{
    margin:0 0 10px
}
 .errorWrap a{
    display:block;
    color:$(main.color);
    padding:10px 0 0
}
 .errorWrap a i{
    font-size:14px
}
 .errorWrap a:hover{
    text-decoration:underline
}
 @media (max-width: 1040px) {
     .row{
        width:100%
    }
    #header-wrap, .home #header-wrap, .item #header-wrap, #intro-author-wrap .container, #content-wrapper, #serv-tile-wrap .container, #contact-area > .container, #footer-copyright > .container, #top-bar .container, .counter-box .container, .featured-posts, #testimonial-wrap .container, #brand-services-wrap .container, #editorial-wrap .container, .project-head{
        box-sizing:border-box;
        padding:0 20px
    }
#slider-section {
    padding: 0 20px;
    box-sizing: border-box;
}
     #intro-services-wrap{
        box-sizing:border-box;
        padding:80px 20px
    }
.counter-box {
    padding: 40px 0;
}
.counter-box-wrap,.counter-title {
border-radius:0;
}
}
 @media (max-width: 980px) {
    .search-icon-action {
        position: absolute;
        right: 40px;
        top: 0;
    }
     #main-menu, #parallax-menu, .home #parallax-menu{
        display:none
    }
     .mobile-menu-wrap,.scrolling-mobile-menu-wrap,.mobile-menu-toggle, .home .scrolling-mobile-menu-toggle{
        display:block
    }
   
.counter-box-info .head-text{
padding:10px;
text-align:center;
}
.counter-box-info {
    text-align: center;
}
#main-intro {
padding-top:68px;
}
.counter-box-item {
    width: 50%;
}
.faq-toggle {
    background-attachment: inherit;
}
     #content-wrapper > .container{
        margin:0
    }
.grid-posts {
    grid-template-columns: repeat(2,1fr);
}
.item #main-wrapper,#sidebar-wrapper{
        width:100%;
        padding:0
    }
#brand-services-wrap li {
    width: calc(100% / 3);
    margin-bottom: 30px;
}
.counter-box-info, .counter-box-image {
    width: 50%;
}
.counter-box-image {
margin-bottom:20px;
}
.counter-image-wrap, .counter-image-wrap .widget, .counter-image-wrap .widget-content, .counter-image-wrap .counter-content {
    
}
.editorial-authors .widget .editorial-avatar-wrap .editorial-avatar {
    height: 310px;
}
#author-email-pic {
    display: block;
    position: absolute;
    right: 0;
    z-index: 1;
    bottom: 0;
    opacity: 0.7;
}
.top-bar-social {
    width: 100%;
    z-index: 9;
    padding: 25px 0;
}
}
 @media (max-width: 780px) {

#serv-tile .widget {
    width: 100%;
    margin-bottom: 10px;
}
.counter-boxs {
}
#serv-tile .widget:nth-child(4n){
width:100%;
}
#serv-tile .widget:nth-child(5n){
width:100%;
}
.author-title {
    display: block;
    font-size: 30px;
}
#intro-author-heading{
padding: 30px 0;
}
  .author-intro-widgets .author-list, .author-intro-widgets .author-list .widget {
    width: 100%;
}
#brand-services-wrap {
    padding: 20px 0;
}
.author-intro-widgets, #serv-tile {
    grid-template-columns: 1fr;
}
#intro-author-heading, .author-intro-widgets {
    width: 100%;
}
#intro-author-photo{
        display:none;
    }
   .post-snippet {
    font-size: 13px;
margin: 0;
}
.index-post .post-info > h2 {
    font-size: 22px;
}
.author-intro-widgets {
    margin: 0;
}
     #social-footer{
        width:100%;
        margin:0 0 10px
    }
     .social-footer ul li{
        margin:0 7px
    }
     #footer-copyright .copyright-area{
        width:100%;
        text-align:center;
        overflow:hidden;
    }
.top-bar-nav {
    width: 100%;
    text-align: center;
    clear: both;
}
.top-bar-nav ul li {
    float: none;
    display: inline-block;
}
     .errorWrap{
        padding:60px 0 80px
    }

}
@media (max-width:767px) {


.head-text .widget-title > h3 {
    line-height: 1.4em;
    display: block;
    font-size: 28px;
}
image-wrap img {
    box-sizing: border-box;
    height: 420px;
}
 .intro-snippet {
    font-size: 13px;
}
.intro-title {
    font-size: 30px;
    margin: 0 0 10px;
}
.intro-action a {
    margin: 10px 0 0;
}
.slide-in {
display:none;
}
}
@media screen and (max-width: 769px) {
#slider-wrapper .PopularPosts .main-slider {
    grid-template-columns: repeat(2,1fr);
}
}
@media screen and (max-width: 681px) {
#slider-wrapper .PopularPosts .main-slider {
        grid-template-columns: 1fr;
    grid-gap: 0px;
}
}
 @media (max-width: 680px) {
     #intro-services .widget{
        width:100%;
        margin:50px 0 0
    }
     #intro-services .widget:first-child{
        margin:0
    }
     #contact-left,#contact-right{
        width:100%
    }
     #contact-left .widget:last-child{
        margin:0 0 35px
    }
     .post-labels{
        width:100%;
        margin:0 0 25px
    }
     .post-labels a{
        margin:0 10px 5px 0
    }
     .post-share{
        float:left
    }
     .share-links li{
        margin:5px 5px 0 0
    }
}
@media screen and (max-width: 560px) {

#intro-wrap {
    height: 560px;
}

.counter-boxs .counter-image-wrap img{
height: 350px;
}

.intro-content {
    text-align:center;
    width:100%;
    height:100%;
    z-index: 10!important;
    position: unset!important;
    -webkit-transform: unset;
    transform: unset;
}
.intro-snippet {
display:block;
}
.intro-title {
color:#000;
}
.intro-snippet{
color:#000;
}

.main-slider .post-title {
    font-size: 18px;
}
.grid-posts {
    grid-template-columns: 1fr;
}
}
 @media (max-width: 540px) {

.counter-box-info, .counter-box-image {
    width: 100%;
}


     #main-intro{
        height:auto
    }
.top-bar-social {
    float: none;
    text-align: center;
}
.top-bar-social ul > li {
    float: none;
}
.author-intro-widgets .author-list {
    width: 100%;
}
#brand-services-wrap {
    padding: 10px 0;
}
.index-post {
    width: 100%;
}
   .feat-big li {
    width: 100%;
    padding: 0;
}
.editorial-authors .widget {
    width: 100%;
    margin-bottom: 15px;
    padding: 0;
}
.editorial-authors .widget:last-child {
    margin: 0;
}
.editorial-authors .widget .editorial-avatar-wrap .editorial-avatar img {
    object-position: top;
}
     .intro-title{
        font-size:27px
    }
     #intro-author-wrap{
        padding:0
    }
     #content-wrapper{
        margin:50px 0
    }
     .item #content-wrapper{
        margin:40px 0
    }
.serv-tile-box-info {
    text-align: center;
}
.email-folower .email-letter-text, .email-folower .follow-by-email-inner {
    float: none;
    width: 100%;
    padding: 0;
}
 ul.related-posts{
        margin:0
    }
     .related-posts .related-item{
        width:100%;
        padding:0;
        margin:20px 0 0
    }
     .related-posts .item-0{
        margin:0
    }
     .related-posts .post-image-link{
        width:75px;
        height:60px;
        float:left;
        margin:0 12px 0 0
    }
     .related-posts .post-title{
        font-size:15px;
        overflow:hidden;
        margin:0 0 5px
    }
#brand-services-wrap li {
    width: calc(100% / 2);
}
}
 @media (max-width: 480px) {
.counter-box-wrap {
    grid-template-columns: 1fr;
}
.counter-box-wrap .widget {
    width: 100%;
    padding: 15px 10px;
}
.deva-svg-shape svg {
    margin-bottom: 5px;
}
}
 @media (max-width: 440px) {
#serv-tile .widget {
    width: 100%;
    margin-bottom: 10px;
}
.counter-box-item {
    width: 100%;
}
     .item-post h1.post-title{
        font-size:23px
    }
     .head-text .widget-content{
        line-height:24px
    }
}
 @media (max-width: 360px) {
     .intro-title,.author-title,.head-text .widget-title > h3{
        font-size:25px
    }
     .index-post{
        width:100%
    }
     
     .errorWrap h3{
        font-size:120px
    }
     .errorWrap h4{
        font-weight:600
    }
}
/* Search Form */
.search-icon-action{display:inline-block;float:right;height:80px;line-height:80px;margin:0 0 0 15px}
.search-toggle{color:$(main.menu.color);font-size:16px}
.search-overlay{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#fff;z-index:99999;display:none;justify-content:flex-start;align-items:center;flex-direction:column;padding-top:15vh;box-sizing:border-box}
.search-close{position:absolute;top:30px;right:30px;color:#000;font-size:30px;cursor:pointer}
.search-close:before{content:'\f00d';font-family:FontAwesome}
.search-form-wrap{position:relative;width:100%;max-width:600px;margin:0 auto;padding:0 20px;box-sizing:border-box}
.search-form{position:relative;width:100%;}
.search-form input{width:100%;height:50px;background-color:transparent;color:#000;font-size:20px;border:0;border-bottom:2px solid #000;padding:0;border-radius:0}
.search-form button{display:none}
.search-suggestion{position:relative;width:100%;max-width:600px;margin:50px auto;padding:0 20px;box-sizing:border-box}
.search-suggestion .title{color:#000;font-size:18px;font-weight:700;margin:0 0 20px}
.search-tags ul{list-style:none;padding:0;margin:0}
.search-tags li{display:inline-block;margin:0 10px 10px 0}
.search-tags a{color:#000;background-color:rgba(0,0,0,0.1);padding:5px 10px;border-radius:3px;font-size:14px}
.search-tags a:hover{background-color:$(main.color);color:#fff}

#editorial-box {
    clear: both;
}
]]></b:skin>
<style>
.firstcharacter{
    float:left;
    color:#27ae60;
    font-size:75px;
    line-height:60px;
    padding-right:8px;
}
 .post-body p{
    margin-bottom:25px
}
 .post-body h1,.post-body h2,.post-body h3,.post-body h4,.post-body h5,.post-body h6{
    color:#000;
    line-height:1.3em;
    margin:0 0 20px
}
 .post-body img{
    height:auto!important
}
 blockquote{
    position:relative;
    background-color:rgba(155,155,155,0.05);
    color:#000000;
    font-style:normal;
    padding:20px 25px;
    margin:0;
    border-radius:3px
}
 blockquote:before{
    position:absolute;
    left:10px;
    top:10px;
    content:&#39;\f10e&#39;;
    font-family:FontAwesome;
    font-size:33px;
    font-style:normal;
    font-weight:900;
    color:#000;
    line-height:1;
    opacity:.05;
    margin:0
}
 .post-body .responsive-video-wrap{
    position:relative;
    width:100%;
    padding:0;
    padding-top:56%
}
 .post-body .responsive-video-wrap iframe{
    position:absolute;
    top:0;
    left:0;
    width:100%;
    height:100%
}
 .post-body ul{
    padding:0 0 0 15px;
    margin:10px 0
}
 .post-body li{
    margin:5px 0;
    padding:0
}
 .post-body ul li,.post-body ol ul li{
    list-style:none
}
 .post-body ul li:before{
    display:inline-block;
    content:&#39;\2022&#39;;
    margin:0 5px 0 0
}
 .post-body ol{
    counter-reset:ify;
    padding:0 0 0 15px;
    margin:10px 0
}
 .post-body ol &gt; li{
    counter-increment:ify;
    list-style:none
}
 .post-body ol &gt; li:before{
    display:inline-block;
    content:counters(ify,&#39;.&#39;)&#39;.&#39;;
    margin:0 5px 0 0
}
 .post-body u{
    text-decoration:underline
}
 .post-body strike{
    text-decoration:line-through
}
 .post-body sup{
    vertical-align:super
}
 .post-body a{
  
}
 .post-body a:hover{
}
 .post-body a.button{
    display:inline-block;
    height:34px;
    background-color:#2c3e50;
    font-size:14px;
    color:#ffffff;
    font-weight:400;
    line-height:34px;
    text-align:center;
    text-decoration:none;
    cursor:pointer;
    padding:0 20px;
    margin:0 6px 8px 0
}
 .post-body a.colored-button{
    color:#fff
}
 .post-body a.button:hover{
    background-color:#f47500;
    color:#fff
}
 .post-body a.colored-button:hover{
    background-color:#f47500!important;
    color:#fff!important
}
 .button:before{
    float:left;
    font-family:FontAwesome;
    font-weight:900;
    display:inline-block;
    margin:0 8px 0 0
}
 .button.preview:before{
    content:&#39;\f06e&#39;
}
 .button.download:before{
    content:&#39;\f019&#39;
}
 .button.link:before{
    content:&#39;\f0c1&#39;
}
 .button.cart:before{
    content:&#39;\f07a&#39;
}
 .button.info:before{
    content:&#39;\f06a&#39;
}
 .button.share:before{
    content:&#39;\f1e0&#39;
}
 .button.contact:before{
    content:&#39;\f0e0&#39;;
    font-weight:400
}
 .alert-message{
    position:relative;
    display:block;
    padding:15px;
    border:1px solid rgba(155,155,155,0.1);
    border-radius:3px
}
 .alert-message.alert-success{
    background-color:rgba(34,245,121,0.03);
    border:1px solid rgba(34,245,121,0.5)
}
 .alert-message.alert-info{
    background-color:rgba(55,153,220,0.03);
    border:1px solid rgba(55,153,220,0.5)
}
 .alert-message.alert-warning{
    background-color:rgba(185,139,61,0.03);
    border:1px solid rgba(185,139,61,0.5)
}
 .alert-message.alert-error{
    background-color:rgba(231,76,60,0.03);
    border:1px solid rgba(231,76,60,0.5)
}
 .alert-message:before{
    font-family:FontAwesome;
    font-size:16px;
    font-weight:900;
    display:inline-block;
    margin:0 5px 0 0
}
 .alert-message.alert-success:before{
    content:&#39;\f058&#39;;
    color:rgba(34,245,121,1)
}
 .alert-message.alert-info:before{
    content:&#39;\f05a&#39;;
    color:rgba(55,153,220,1)
}
 .alert-message.alert-warning:before{
    content:&#39;\f06a&#39;;
    color:rgba(185,139,61,1)
}
 .alert-message.alert-error:before{
    content:&#39;\f057&#39;;
    color:rgba(231,76,60,1)
}
 .post-body table{
    width:100%;
    overflow-x:auto;
    text-align:left;
    margin:0;
    border-collapse:collapse;
    border:1px solid #161619
}
 
 .post-body table td,.post-body table th{
    padding:7px 15px;
    border:1px solid #161619
}
 .post-body table thead th{
    color:#000;
    font-weight:700;
    text-align:left;
    vertical-align:bottom
}
 table.tr-caption-container,table.tr-caption-container td,table.tr-caption-container th{
    line-height:1;
    padding:0;
    border:0
}
 table.tr-caption-container td.tr-caption{
    font-size:13px;
    color:#666666;
    padding:6px 0 0
}
 .tocify-wrap{
    display:flex;
    width:100%;
    clear:both;
    margin:0
}
 .tocify-inner{
    position:relative;
    max-width:100%;
    background-color:rgba(155,155,155,0.05);
    display:flex;
    flex-direction:column;
    overflow:hidden;
    font-size:14px;
    color:#000000;
    line-height:1.6em;
    border:1px solid rgba(155,155,155,0.1);
    border-radius:3px
}
 a.tocify-title{
    position:relative;
    height:38px;
    font-size:16px;
    color:#000000;
    font-weight:700;
    display:flex;
    align-items:center;
    justify-content:space-between;
    padding:0 15px;
    margin:0
}
 .tocify-title-text{
    display:flex
}
 .tocify-title-text:before{
    content:&#39;\f0cb&#39;;
    font-family:FontAwesome;
    font-size:14px;
    font-weight:900;
    margin:0 6px 0 0
}
 .tocify-title:after{
    content:&#39;\f078&#39;;
    font-family:FontAwesome;
    font-size:12px;
    font-weight:900;
    margin:0 0 0 25px
}
 .tocify-title.is-expanded:after{
    content:&#39;\f077&#39;
}
 a.tocify-title:hover{
    text-decoration:none
}
 #tocify{
    display:none;
    padding:0 15px 10px;
    margin:0
}
 #tocify ol{
    padding:0 0 0 15px
}
 .rtl #tocify ol{
    padding:0 15px 0 0
}
 #tocify li{
    font-size:14px;
    margin:8px 0
}
 #tocify li a{
    color:#f47500
}
 #tocify li a:hover{
    color:#f47500;
    text-decoration:underline
}
 .post-body .contact-form{
    display:table;
}
 .contact-form .widget-title{
    display:none
}
 .contact-form .contact-form-name{
    width:calc(50% - 5px)
}
 .contact-form .contact-form-email{
    float:right;
    width:calc(50% - 5px)
}
 .post-body pre,pre.code-box{
    position:relative;
    display:block;
    background-color:rgba(155,155,155,0.05);
    font-family:Monospace;
    font-size:13px;
    color:#47474a;
    white-space:pre-wrap;
    line-height:1.4em;
    padding:15px;
    margin:0;
    border:1px solid rgba(155,155,155,0.1);
    border-radius:3px
}
 .post-body .google-auto-placed{
    margin:25px 0
}
  #hidden-widgets-wrap,.hidden-widgets{
    display:none;
    visibility:hidden
}
  @media only screen and (max-width: 680px) {
.post-body table {
    display: block;
}
  }
</style>
<b:if cond='data:view.isLayoutMode'>
<b:template-skin>
<![CDATA[
/*------Layout (No Edit)----------*/
body#layout {
width:800px;
}
body#layout #preloader {
display:none;
}
body#layout .counter-box {
    padding: 0;
}
body#layout #outer-wrapper,body#layout .row{
    width:auto;
    padding:0
}
 body#layout{
    width:800px;
    position:relative;
    padding:95px 5px 0;
    margin:0
}
 body#layout div.section{
    margin:0 5px 10px!important;
    padding:16px 16px 18px!important
}
body#layout #brand-services-wrap {
    display: block;
    padding: 0;
}
 body#layout .section h4{
    font-size:14px;
    margin:0
}
body#layout .email-folower {
    padding: 0;
}
 body#layout .layout-widget-description{
    display:none
}
 body#layout .theme-options,body#layout #main-menu .widget{
    display:block!important
}
 body#layout div.sora-panel{
    background-color:#d7d7d7!important;
    overflow:hidden!important;
    border-color:#bcbcbc
}
body#layout #intro-author .widget-content {
    height: 224px;
}
 body#layout #header-wrap{
    height:auto;
position: static;
}
body#layout .author-intro-widgets {
  
}

 body#layout .header-header{
    padding:0
}
 body#layout div.header-logo{
    float:none;
    width:auto;
    height:auto;
    max-width:none
}
 body#layout .mobile-menu{
    display:none
}
 body#layout #header-wrap .container{
    display:flex
}
 body#layout #header-logo,body#layout #parallax-menu, body#layout #main-menu{
    width:33.333%;
    height:auto
}
 body#layout #intro-wrap,body#layout #intro-author-wrap,body#layout #intro-services-wrap,body#layout #serv-tile-wrap,body#layout #contact-area{
    display:block;
    padding:0;
    margin:0
}
 body#layout #intro-wrap,body#layout #main-intro{
    float:none;
    width:auto;
    height:auto
}
 body#layout #main-intro .widget{
    height:auto;
    overflow:visible
}
 body#layout #intro-author-photo{
    display:none
}
 body#layout #intro-author{
    width:auto
}
 body#layout #content-wrapper{
    margin:0
}
 body#layout #content-wrapper > .container{
    margin:0
}
 body#layout .head-text,body#layout #serv-tile{
    float:none;
    width:auto
}
 body#layout #main-wrapper{
   width: 100%;
    padding: 0;
    margin: 0;
    float: left;
}
 body#layout #contact-area .container{
    display:flex
}
 body#layout #contact-area .section{
    width:50%
}
 body#layout .contact-col .Text .widget-content{
    margin:0
}
 body#layout #sidebar-wrapper{
float:right;
  display: block;
    width: 35%;
    padding: 0;
}
body#layout .sidebar .widget,body#layout .sidebar .widget-content{float:none;overflow:visible;width:auto;}
body#layout .top-bar-nav, body#layout .top-bar-social {
    width: auto;
float:none;
}
body#layout #top-bar {
    height: auto;
}
body#layout #serv-tile {
    display: block;
}
body#layout .author-intro-widgets {
    display: flex;
}
body#layout .author-intro-widgets .author-list {
  width: 33.333%;
    float: left;
}
 body#layout #footer-copyright{
    padding:0
}
body#layout .top-bar-social .FollowByEmail {
    max-width: 100%;
}
body#layout #intro-author-heading {
    width: auto;
    float: none;
    display: block;
}
body#layout #intro-author-wrap .container {
   display:block;
}
body#layout .author-intro-widgets {
margin:0 auto;
}
body#layout .counter-box .container {
    display: flex;
}
body#layout .counter-box-info, body#layout .counter-box-image {
    float: left;
    width: 50%;
}
body#layout .counter-box-image {
    display: flex;
}
body#layout .counter-image-wrap {
    width: 100%;
}
body#layout .counter-box-image .widget-content {
    height: 22em;
}
/*------Layout (end)----------*/
]]></b:template-skin>
</b:if>
    <b:if cond='data:view.isHomepage'> 
    </b:if>
<!-- Global Variables -->
<script type='text/javascript'>
//<![CDATA[
// Global variables with content. "Available for Edit"
var monthFormat = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
    noThumbnail = "https://4.bp.blogspot.com/-O3EpVMWcoKw/WxY6-6I4--I/AAAAAAAAB2s/KzC0FqUQtkMdw7VzT6oOR_8vbZO6EJc-ACK4BGAYYCw/s1600/nth.png",
    postPerPage = 8,
    commentsSystem = "blogger",
    disqusShortname = "soratemplates";
//]]>
</script>

<b:defaultmarkups>
  <b:defaultmarkup type='Common'>
    <b:includable id='widget-title'>
      <b:if cond='data:defaultTitle or data:title'>
        <div class='widget-title'>
          <h3 class='title'>
            <data:title/>
          </h3>
        </div>
      </b:if>
    </b:includable> 
     <b:includable id='contact-form-content'>
      <div class='widget-content contact-form-widget'>
        <div class='form'>
          <form name='contact-form'>
            <input class='contact-form-name' expr:ariby='data:contactFormNameMsg' expr:id='data:widget.instanceId + &quot;_contact-form-name&quot;' expr:placeholder='data:contactFormNameMsg' name='name' size='30' type='text' value=''/>
            <input class='contact-form-email' expr:ariby='data:contactFormEmailMsg + &quot; *&quot;' expr:id='data:widget.instanceId + &quot;_contact-form-email&quot;' expr:placeholder='data:contactFormEmailMsg + &quot; *&quot;' name='email' size='30' type='text' value=''/>
            <textarea class='contact-form-email-message' cols='25' expr:ariby='data:contactFormMessageMsg + &quot; *&quot;' expr:id='data:widget.instanceId + &quot;_contact-form-email-message&quot;' expr:placeholder='data:contactFormMessageMsg + &quot; *&quot;' name='email-message' rows='5'/>
            <input class='contact-form-button btn contact-form-button-submit' expr:id='data:widget.instanceId + &quot;_contact-form-submit&quot;' expr:value='data:contactFormSendMsg' type='button'/>
            <p class='contact-form-error-message' expr:id='data:widget.instanceId + &quot;_contact-form-error-message&quot;'/>
            <p class='contact-form-success-message' expr:id='data:widget.instanceId + &quot;_contact-form-success-message&quot;'/>
          </form>
        </div>
      </div>
    </b:includable> 
  </b:defaultmarkup>
  <b:defaultmarkup type='PopularPosts'>
    <b:includable id='main' var='this'>
      <b:include name='widget-title'/>
      <div class='widget-content'>
          <b:tag class='main-slider' cond='data:widget.sectionId == &quot;slider-section&quot;' name='ul'>
        <b:loop index='i' values='data:posts' var='post'>
          <b:include data='post' name='postContent'/>
        </b:loop>
            </b:tag>
      </div>
    </b:includable>
    <b:includable id='postContent' var='post'>
       <b:if cond='data:widget.sectionId == &quot;slider-section&quot;'>
        <b:include data='post' name='slider-section'/>
        <b:else/>
        <b:include data='post' name='default'/>
      </b:if>
    </b:includable>
     <b:includable id='slider-section' var='post'>
         <b:if cond='data:i lt 10'>
           <li class='slider-item'>
              <b:class expr:name='&quot;item-&quot;+data:i'/>
               <a class='post-image-link' expr:href='data:post.url'>
            <b:if cond='data:post.featuredImage'>
              <img class='post-thumb' expr:alt='data:post.title' expr:src='data:post.featuredImage.isYouTube ? resizeImage(data:post.featuredImage.youtubeMaxResDefaultUrl.jsonEscaped, 72, &quot;1:1&quot;) : resizeImage(data:post.featuredImage, 72, &quot;1:1&quot;)'/>
              <b:else/>
              <img class='post-thumb' expr:alt='data:post.title' src='https://4.bp.blogspot.com/-O3EpVMWcoKw/WxY6-6I4--I/AAAAAAAAB2s/KzC0FqUQtkMdw7VzT6oOR_8vbZO6EJc-ACK4BGAYYCw/w680/nth.png'/>
            </b:if>
          </a>
              <div class='post-info-wrap'><div class='post-info'>
              <b:if cond='data:post.labels'><span class='post-tag'><data:post.labels.first.name/></span></b:if>
               <h2 class='post-title'>
               <a expr:href='data:post.url'><data:post.title/></a>
               </h2>
               </div></div>
           </li>
           </b:if>
       </b:includable>
    <b:includable id='default' var='post'>
       <div class='post default-popularpost'>  
              <b:class expr:name='&quot;item-&quot;+data:i'/>
        <div class='post-content'>
          <a class='post-image-link' expr:href='data:post.url'>
            <b:if cond='data:post.featuredImage'>
              <img class='post-thumb' expr:alt='data:post.title' expr:src='data:post.featuredImage.isYouTube ? resizeImage(data:post.featuredImage.youtubeMaxResDefaultUrl.jsonEscaped, 72, &quot;1:1&quot;) : resizeImage(data:post.featuredImage, 72, &quot;1:1&quot;)'/>
              <b:else/>
              <img class='post-thumb' expr:alt='data:post.title' src='https://4.bp.blogspot.com/-O3EpVMWcoKw/WxY6-6I4--I/AAAAAAAAB2s/KzC0FqUQtkMdw7VzT6oOR_8vbZO6EJc-ACK4BGAYYCw/w680/nth.png'/>
            </b:if>
          </a>
          <div class='post-info'>
            <h2 class='post-title'>
              <a expr:href='data:post.url'><data:post.title/></a>
            </h2>
            <div class='post-meta'>
              <span class='post-date published' expr:datetime='data:post.date.iso8601'><data:post.date/></span>
            </div>
          </div>
        </div>
      </div>
    </b:includable>    
  </b:defaultmarkup>
  <b:defaultmarkup type='Header'>
    <b:includable id='main' var='this'>
      <div class='header-widget'>
        <b:include cond='data:imagePlacement in {&quot;REPLACE&quot;, &quot;BEFORE_DESCRIPTION&quot;}' name='image'/>
        <b:include cond='data:imagePlacement not in {&quot;REPLACE&quot;, &quot;BEFORE_DESCRIPTION&quot;}' name='title'/>
        <b:include cond='data:imagePlacement != &quot;REPLACE&quot;' name='description'/>
      </div>
    </b:includable>
    <b:includable id='description'>
      <p>
        <data:this.description/>
      </p>
    </b:includable>
    <b:includable id='image'>
      <a class='header-brand' expr:href='data:blog.homepageUrl'>
        <img expr:alt='data:blog.title.escaped' expr:data-height='data:height' expr:data-width='data:width' expr:src='data:image'/>
      </a>
    </b:includable>
    <b:includable id='title'>
      <h1>
        <b:tag cond='data:view.url != data:blog.homepageUrl' expr:href='data:blog.homepageUrl' name='a'>
          <data:title/>
        </b:tag>
      </h1>
    </b:includable>
  </b:defaultmarkup>
  <b:defaultmarkup type='Label'>
    <b:includable id='main' var='this'>
      <b:include name='widget-title'/>
      <b:include name='content'/>
    </b:includable>
    <b:includable id='content'>
      <div class='widget-content'>
        <b:class expr:name='data:this.display + &quot;-label&quot;'/>
        <b:include cond='data:this.display == &quot;list&quot;' name='list'/>
        <b:include cond='data:this.display == &quot;cloud&quot;' name='list'/>
      </div>
    </b:includable>
    <b:includable id='list'>
      <ul>
        <b:loop values='data:labels' var='label'>
          <li>
            <a class='label-name' expr:href='data:label.url'>
              <data:label.name/>
              <b:if cond='data:this.showFreqNumbers'>
                <span class='label-count'>(<data:label.count/>)</span>
              </b:if>
            </a>
          </li>
        </b:loop>
      </ul>
    </b:includable>
  </b:defaultmarkup>
</b:defaultmarkups>

    <!-- Google Analytics -->
    <b:include data='blog' name='google-analytics'/>

</head>
<body expr:class='data:blog.pageType'>
  <b:class cond='data:view.isHomepage' name='home'/>
  <b:class cond='data:view.isPage' name='item'/>
  <b:class cond='data:view.isArchive' name='index'/>
  <b:class cond='data:view.isError' name='error404'/>
<!--preloader start-->
<div id='preloader'>
<div id='loader'>
<span class='loader-main'><span class='loader-inner'/></span>
</div>
</div>
<!--preloader end-->
<!-- Theme Options -->
  <div class='theme-options' style='display:none'>
    <b:section class='sora-panel' id='sora-panel' maxwidgets='1' name='Theme Options' showaddelement='no'>
      <b:widget id='LinkList71' locked='true' title='Default Variables' type='LinkList' version='2' visible='true'>
        <b:widget-settings>
          <b:widget-setting name='link-3'>8</b:widget-setting>
          <b:widget-setting name='sorting'>NONE</b:widget-setting>
          <b:widget-setting name='link-4'>5</b:widget-setting>
          <b:widget-setting name='text-1'>commentsSystem</b:widget-setting>
          <b:widget-setting name='link-1'>blogger</b:widget-setting>
          <b:widget-setting name='text-0'>disqusShortname</b:widget-setting>
          <b:widget-setting name='link-2'>8</b:widget-setting>
          <b:widget-setting name='text-3'>postPerPage</b:widget-setting>
          <b:widget-setting name='link-0'>soratemplates</b:widget-setting>
          <b:widget-setting name='text-2'>postPerPage</b:widget-setting>
          <b:widget-setting name='text-4'>postPerPage</b:widget-setting>
        </b:widget-settings>
        <b:includable id='main'>
          <b:include name='content'/>
        </b:includable>
        <b:includable id='content'>
          &lt;script type=&#39;text/javascript&#39;&gt;
          //&lt;![CDATA[
          <b:loop values='data:links' var='link'>
            <b:if cond='data:link.name == &quot;postPerPage&quot;'>
              var postPerPage = <data:link.target/>;
            </b:if>
            <b:if cond='data:link.name == &quot;commentsSystem&quot;'>
              var commentsSystem = &quot;<data:link.target/>&quot;;
            </b:if>
            <b:if cond='data:link.name == &quot;disqusShortname&quot;'>
              var disqusShortname = &quot;<data:link.target/>&quot;;
            </b:if>
          </b:loop>
          //]]&gt;
          &lt;/script&gt;
        </b:includable>
      </b:widget>
    </b:section>
  </div>

<!-- Outer Wrapper -->
<div id='outer-wrapper'>
<div class='search-overlay' style='display:none;'>
  <span class='search-close'/>
  <div class='search-form-wrap'>
    <form class='search-form' expr:action='data:blog.homepageUrl + &quot;search&quot;' method='get'>
      <input autocomplete='off' name='q' placeholder='Search...' type='text'/>
      <button class='search-submit' type='submit'>Search</button>
    </form>
  </div>
  <div class='search-suggestion'>
    <h3 class='title'>Search Suggest</h3>
    <div class='search-tags' id='search-tags'/>
  </div>
</div>
  <!-- Header Wrapper -->
  <div id='header-wrap'>
    <div class='container row'>
      <b:section class='header-logo' id='header-logo' maxwidgets='1' name='Header Logo' showaddelement='yes'>
        <b:widget id='Header1' locked='true' title='عالم العطور (Header)' type='Header' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='displayUrl'>https://blogger.googleusercontent.com/img/a/AVvXsEiT6EMz4khNRC8LBqt2wza5-a0kePLKg2tD9iDNmASm0n9qEhyNP4XzyEuO8zlVq4rcNiiexflbrn6eSXlsnuDXWX_Mrp8Fc_MCErh84icFC_3kc3YvbmRhEkPIijckh-YWoYjoC6To-5Jq3iliNbkHU0qOi8HASXds0tUNarq4WHcVxF4fcfrSPoe1=s119</b:widget-setting>
            <b:widget-setting name='displayHeight'>58</b:widget-setting>
            <b:widget-setting name='sectionWidth'>150</b:widget-setting>
            <b:widget-setting name='useImage'>true</b:widget-setting>
            <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
            <b:widget-setting name='imagePlacement'>REPLACE</b:widget-setting>
            <b:widget-setting name='displayWidth'>119</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main' var='this'>
            <div class='header-widget'>
              <b:include cond='data:imagePlacement in {&quot;REPLACE&quot;, &quot;BEFORE_DESCRIPTION&quot;}' name='image'/>
              <b:include cond='data:imagePlacement not in {&quot;REPLACE&quot;, &quot;BEFORE_DESCRIPTION&quot;}' name='title'/>
              <b:include cond='data:imagePlacement != &quot;REPLACE&quot;' name='description'/>
            </div>
          </b:includable>
          <b:includable id='behindImageStyle'>
            <b:if cond='data:sourceUrl'>
              <b:include cond='data:this.image' data='{                    image: data:this.image,                    selector: &quot;.header-widget&quot;                  }' name='responsiveImageStyle'/>
              <style type='text/css'>
                .header-widget {
                  background-position: <data:blog.locale.languageAlignment/>;
                  background-repeat: no-repeat;
                }
              </style>
            </b:if>
          </b:includable>
          <b:includable id='description'>
            <p>
              <data:this.description/>
            </p>
          </b:includable>
          <b:includable id='image'>
            <a class='header-brand' expr:href='data:blog.homepageUrl'>
              <img expr:alt='data:blog.title.escaped' expr:data-height='data:height' expr:data-width='data:width' expr:src='data:image'/>
            </a>
          </b:includable>
          <b:includable id='title'>
            <h1>
              <b:tag cond='data:view.url != data:blog.homepageUrl' expr:href='data:blog.homepageUrl' name='a'>
                <data:title/>
              </b:tag>
            </h1>
          </b:includable>
        </b:widget>
      </b:section>
      <b:section class='scrolling-menu' id='parallax-menu' maxwidgets='1' name='Scrolling Menu' showaddelement='yes'>
        <b:widget id='LinkList1' locked='true' title='Menu' type='LinkList' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='shownum'>6</b:widget-setting>
            <b:widget-setting name='sorting'>NONE</b:widget-setting>
            <b:widget-setting name='text-0'>Home</b:widget-setting>
            <b:widget-setting name='link-0'>#header-wrap</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
  <b:include name='widget-title'/>
  <b:include name='content'/>
</b:includable>
          <b:includable id='content'>
 <div class='widget-content'>
   <ul>
     <b:loop values='data:links' var='link'>
       <li><a expr:href='data:link.target'><data:link.name/></a></li>
     </b:loop>
   </ul>
 </div>
</b:includable>
        </b:widget>
      </b:section>
      <span class='scrolling-mobile-menu-toggle'/> 
      <b:section class='main-menu' id='main-menu' maxwidgets='1' name='Main Menu' showaddelement='yes'>
         <b:widget id='LinkList74' locked='true' title='Menu Widget' type='LinkList' version='2' visible='true'>
           <b:includable id='main'>
            <b:include name='content'/>
          </b:includable>
           <b:includable id='content'>
            <ul id='main-menu-nav' role='menubar'>
              <b:loop values='data:links' var='link'>
                <li><a expr:href='data:link.target' role='menuitem'><data:link.name/></a></li>
              </b:loop>
            </ul>
          </b:includable>
         </b:widget>
       </b:section>
      <div class='search-icon-action'>
        <a class='search-toggle' href='javascript:void(0)'><i class='fa fa-search'/></a>
      </div>
      <span class='mobile-menu-toggle'/> 
    </div>
    <div class='scrolling-mobile-menu-wrap'>
      <div class='scrolling-mobile-menu'/>
    </div>
    <div class='mobile-menu-wrap'>
      <div class='mobile-menu'/>
    </div>
  </div>

  <div class='clearfix'/>
  <b:if cond='data:view.isHomepage'> 
    <!-- Featured Slider -->
    <div id='intro-wrap'>
      <b:section class='full-height' id='main-intro' maxwidgets='1' name='Main Intro' showaddelement='no'>
        <b:widget id='Image1' locked='true' title='عالم العطور' type='Image' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='displayUrl'>https://blogger.googleusercontent.com/img/a/AVvXsEier_spSVTT8oyvWUKac6-BRQ7kID3FSGLpgY2tiHDKtqdaIOR5pbOBmdyDzwQNGywKhxOJ3RM1b5WQauJtiMagh3z3ripabd8A27TfU1c1HvHfqTJJKZAyjNH2jypHsZyZBKN3pxfwi9de62AVDQupCE0czDn45OlLrNYy1D27bNJFku65igS1giQYJ7XC=s1600</b:widget-setting>
            <b:widget-setting name='displayHeight'>1067</b:widget-setting>
            <b:widget-setting name='sectionWidth'>150</b:widget-setting>
            <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
            <b:widget-setting name='displayWidth'>1600</b:widget-setting>
            <b:widget-setting name='link'>/#Know More</b:widget-setting>
            <b:widget-setting name='caption'>It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <b:include name='content'/>
          </b:includable>
          <b:includable id='content'>
            <div class='container widget-content'>
              &lt;style type=&#39;text/css&#39;&gt;
              #intro-wrap{display:block}
              &lt;/style&gt;
               <div class='intro-image'><img expr:src='data:sourceUrl'/></div>
              <div class='intro-content'>
                <h3 class='intro-title'><data:title/></h3>
                <b:if cond='data:caption'>
                  <p class='intro-snippet'><data:caption/></p>
                </b:if>
                <b:if cond='data:link'>
                  <div class='intro-action'>
                    &lt;script type=&quot;text/javascript&quot;&gt;
                    var ilc = &quot;<data:link/>&quot;,
                    ima = ilc.split(&quot;#&quot;),
                    ili = ima[0].trim(),
                    ilt = ima[1].trim(),
                    kod = &quot;&lt;a href=&quot;+ ili +&quot;&gt;&quot;+ ilt +&quot;&lt;/a&gt;&quot;;
                    document.write(kod)
                    &lt;/script&gt;
                  </div>
                </b:if>
              </div>
             
            </div>
        </b:includable>
        </b:widget>
      </b:section>
    </div>
  </b:if>
  <div class='clearfix'/>

  <!-- Counter Boxes Section -->
  <div class="counter-boxs">
    <div class="container row">
      <div class="counter-box-image wow fadeInLeftBig" style="height: 523px;">
        <b:section class="counter-image-wrap" id="counter-image-navv" maxwidgets="1" name="Featured Image" showaddelement="yes">
          <b:widget id='Image3' locked='false' title='Featured Image' type='Image' version='2' visible='true'>
            <b:widget-settings>
              <b:widget-setting name='displayUrl'>https://blogger.googleusercontent.com/img/a/AVvXsEj84SOANwD32G34-wCUMy722f1UOCcbrFvNxlXHqBojnfDTf0gR1Skyn0rgucqV-UgyhfcrAe4MfX_7LVt9bcOK6eD2gKl0fK-7mwWxsFPqLWqjNPaOC-vVKo4QOLOBEduAQ1ens-uV3DnY7Ai9WtwHEVRdX0PAv3UyWl6QmwPDTMH-3RNnVJLSr4OF=s1024</b:widget-setting>
              <b:widget-setting name='displayHeight'>1024</b:widget-setting>
              <b:widget-setting name='displayWidth'>714</b:widget-setting>
              <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
              <b:widget-setting name='sectionWidth'>714</b:widget-setting>
            </b:widget-settings>
            <b:includable id='main'>
              <div class='widget-content'>
                <img expr:alt='data:title' expr:height='data:height' expr:src='data:sourceUrl' expr:width='data:width'/>
                <br/>
              </div>
            </b:includable>
          </b:widget>
        </b:section>
      </div>
      <div class="counter-box-info wow fadeInRightBig" style="height: 523px;">
        <b:section class="menu-box-wrap" id="menu-bar-navv" maxwidgets="1" name="Menu-List" showaddelement="yes">
          <b:widget id='Image2' locked='false' title='Menu Button' type='Image' version='2' visible='true'>
            <b:widget-settings>
              <b:widget-setting name='displayUrl'>https://blogger.googleusercontent.com/img/a/AVvXsEjLXBEzCB9tmeKAMZyyzos3z_qGmgt33cT2ijMHnH2oAlv7bglXoaSVAOUO7R_W2qJQ1Z1SiPWhr_cECPSwuUrSrMNxJIOJxgLu0ClJkH03shjyvdbSx9M_iWDBPaBt11wos2XgNHUckhrqvxzkmot9V-HeEBBEHD1QZqBZTA1MueYsbEEhehMsEnLd=s180</b:widget-setting>
              <b:widget-setting name='displayHeight'>120</b:widget-setting>
              <b:widget-setting name='displayWidth'>180</b:widget-setting>
              <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
              <b:widget-setting name='sectionWidth'>180</b:widget-setting>
              <b:widget-setting name='link'>/#Know More</b:widget-setting>
            </b:widget-settings>
            <b:includable id='main'>
              <div class='widget-content'>
                <b:if cond='data:link'>
                  <a expr:href='data:link'>
                    <img expr:alt='data:title' expr:height='data:height' expr:src='data:sourceUrl' expr:width='data:width'/>
                  </a>
                <b:else/>
                  <img expr:alt='data:title' expr:height='data:height' expr:src='data:sourceUrl' expr:width='data:width'/>
                </b:if>
                <br/>
              </div>
            </b:includable>
          </b:widget>
        </b:section>
        <b:section class="head-text" id="head-text44" maxwidgets="1" name="Headline Text 01" showaddelement="yes">
          <b:widget id='Text444' locked='false' title='Strawberry Flavor For The Holiday' type='Text' version='2' visible='true'>
            <b:widget-settings>
              <b:widget-setting name='content'><![CDATA[It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here'. Lorem Ipsum has been the industry's standard dummy text ever since.]]></b:widget-setting>
            </b:widget-settings>
            <b:includable id='main'>
              <b:include name='widget-title'/>
              <p class='widget-content'><data:content/></p>
              <div class="buttonn">
                <a class="medium large" href="https://www.blogger.com/null">Read More</a>
              </div>
            </b:includable>
          </b:widget>
        </b:section>
      </div>
      <div class="clearfix"></div>
    </div>
  </div>

  <!-- Service Tiles Section -->
  <div class="wow slideInUp" data-wow-delay="1.5s" data-wow-duration="1.5s" id="serv-tile-wrap">
    <div class="container row">
      <b:section id="info-tile" maxwidgets="1" name="Shop List" showaddelement="yes">
        <b:widget id='HTML5' locked='false' title='Why Our Ice Cream Are The Best' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'><![CDATA[Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo, viva la vida.]]></b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <b:include name='widget-title'/>
            <div class='widget-content'><data:content/></div>
          </b:includable>
        </b:widget>
      </b:section>
      <b:section id="serv-tile" maxwidgets="10" name="Service List" showaddelement="yes">
        <b:widget id='Image6' locked='false' title='Fresh Fruits' type='Image' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='displayUrl'>https://via.placeholder.com/100x100/dfc78f/ffffff?text=Icon</b:widget-setting>
            <b:widget-setting name='displayHeight'>100</b:widget-setting>
            <b:widget-setting name='displayWidth'>100</b:widget-setting>
            <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
            <b:widget-setting name='sectionWidth'>100</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='widget-content'>
              <style type="text/css">
                #serv-tile-wrap{display:block}
              </style>
              <div class="serv-tile-box-avatar"><i class="fa fa fa-leaf"></i></div>
              <div class="serv-tile-box-info">
                <h3 class="serv-tile-box-title">Fresh Fruits</h3>
                <p class="serv-tile-box-meta">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar.</p>
              </div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='Image7' locked='false' title='Organic Ingredients' type='Image' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='displayUrl'>https://via.placeholder.com/100x100/dfc78f/ffffff?text=Icon</b:widget-setting>
            <b:widget-setting name='displayHeight'>100</b:widget-setting>
            <b:widget-setting name='displayWidth'>100</b:widget-setting>
            <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
            <b:widget-setting name='sectionWidth'>100</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='widget-content'>
              <style type="text/css">
                #serv-tile-wrap{display:block}
              </style>
              <div class="serv-tile-box-avatar"><i class="fa fa fa-apple"></i></div>
              <div class="serv-tile-box-info">
                <h3 class="serv-tile-box-title">Organic Ingredients</h3>
                <p class="serv-tile-box-meta">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar.</p>
              </div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='Image8' locked='false' title='Sustainable Milk' type='Image' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='displayUrl'>https://via.placeholder.com/100x100/dfc78f/ffffff?text=Icon</b:widget-setting>
            <b:widget-setting name='displayHeight'>100</b:widget-setting>
            <b:widget-setting name='displayWidth'>100</b:widget-setting>
            <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
            <b:widget-setting name='sectionWidth'>100</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='widget-content'>
              <style type="text/css">
                #serv-tile-wrap{display:block}
              </style>
              <div class="serv-tile-box-avatar"><i class="fa fa fa-bullseye"></i></div>
              <div class="serv-tile-box-info">
                <h3 class="serv-tile-box-title">Sustainable Milk</h3>
                <p class="serv-tile-box-meta">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar.</p>
              </div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='Image888' locked='false' title='Friendly Staff' type='Image' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='displayUrl'>https://via.placeholder.com/100x100/dfc78f/ffffff?text=Icon</b:widget-setting>
            <b:widget-setting name='displayHeight'>100</b:widget-setting>
            <b:widget-setting name='displayWidth'>100</b:widget-setting>
            <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
            <b:widget-setting name='sectionWidth'>100</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='widget-content'>
              <style type="text/css">
                #serv-tile-wrap{display:block}
              </style>
              <div class="serv-tile-box-avatar"><i class="fa fa fa-users"></i></div>
              <div class="serv-tile-box-info">
                <h3 class="serv-tile-box-title">Friendly Staff</h3>
                <p class="serv-tile-box-meta">Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem.</p>
              </div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='Image999' locked='false' title='Open During Holidays' type='Image' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='displayUrl'>https://via.placeholder.com/100x100/dfc78f/ffffff?text=Icon</b:widget-setting>
            <b:widget-setting name='displayHeight'>100</b:widget-setting>
            <b:widget-setting name='displayWidth'>100</b:widget-setting>
            <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
            <b:widget-setting name='sectionWidth'>100</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='widget-content'>
              <style type="text/css">
                #serv-tile-wrap{display:block}
              </style>
              <div class="serv-tile-box-avatar"><i class="fa fa fa-calendar-check-o"></i></div>
              <div class="serv-tile-box-info">
                <h3 class="serv-tile-box-title">Open During Holidays</h3>
                <p class="serv-tile-box-meta">Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem</p>
              </div>
            </div>
          </b:includable>
        </b:widget>
      </b:section>
    </div>
  </div>

  <!-- Counter Box Section -->
  <div class="counter-box">
    <div class="container row">
      <div class="counter-box-image wow fadeInRightBig" style="height: 423px;">
        <b:section class="counter-image-wrap" id="counter-image-nav" maxwidgets="1" name="Featured Image" showaddelement="yes">
          <b:widget id='Image14' locked='false' title='Background Image' type='Image' version='2' visible='true'>
            <b:widget-settings>
              <b:widget-setting name='displayUrl'>https://blogger.googleusercontent.com/img/a/AVvXsEgWfBfRGXN3Ss8tQh64Yc0AxZy_x_QC8WzAOnlXT64RUMQUT4C_HmoUNtQivTgjfF1U_dhb8_GVbtNFnCx2zVNa7CWtX6f9wH8fSA9S1DxXCNyV3YOJSPMGzJy4ekepHdLh5F7-bNjKS_fAqxWoiA8Gy_UZXFpBWdaFVlh0lZI8ieO35C5uCg9-ODFuLQ=s806</b:widget-setting>
              <b:widget-setting name='displayHeight'>423</b:widget-setting>
              <b:widget-setting name='displayWidth'>806</b:widget-setting>
              <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
              <b:widget-setting name='sectionWidth'>806</b:widget-setting>
            </b:widget-settings>
            <b:includable id='main'>
              <div class='widget-content'>
                <div class="counter-content">
                  <style type="text/css">
                    .counter-box-image .counter-content{background-image: url(<data:sourceUrl/>);}
                  </style>
                </div>
              </div>
            </b:includable>
          </b:widget>
        </b:section>
      </div>
      <div class="counter-box-info wow fadeInLeftBig" style="height: 423px;">
        <b:section class="head-text" id="head-text4" maxwidgets="1" name="Headline Text 01" showaddelement="yes">
          <b:widget id='Text4' locked='false' title='Our Menu' type='Text' version='2' visible='true'>
            <b:widget-settings>
              <b:widget-setting name='content'><![CDATA[It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here'. Lorem Ipsum has been the industry's standard dummy text ever since.]]></b:widget-setting>
            </b:widget-settings>
            <b:includable id='main'>
              <b:include name='widget-title'/>
              <p class='widget-content'><data:content/></p>
            </b:includable>
          </b:widget>
        </b:section>
        <b:section class="menu-box-wrap" id="menu-bar-nav" maxwidgets="1" name="Menu-List" showaddelement="yes">
          <b:widget id='LinkList2' locked='false' title='Menu Items' type='LinkList' version='2' visible='true'>
            <b:widget-settings>
              <b:widget-setting name='sorting'>NONE</b:widget-setting>
              <b:widget-setting name='text-1'>Vanilla Ice Cream</b:widget-setting>
              <b:widget-setting name='link-1'>#</b:widget-setting>
              <b:widget-setting name='text-2'>Mango Ice Cream</b:widget-setting>
              <b:widget-setting name='link-2'>#</b:widget-setting>
              <b:widget-setting name='text-3'>Tropical Fruit Ice Cream</b:widget-setting>
              <b:widget-setting name='link-3'>#</b:widget-setting>
              <b:widget-setting name='text-4'>Chocolate Ice Cream</b:widget-setting>
              <b:widget-setting name='link-4'>#</b:widget-setting>
              <b:widget-setting name='text-5'>Vegan Ice Cream</b:widget-setting>
              <b:widget-setting name='link-5'>#</b:widget-setting>
            </b:widget-settings>
            <b:includable id='main'>
              <div class='widget-content'>
                <ul>
                  <li><a class="nn"><i class="fa fa-check" aria-hidden="true"></i>Vanilla Ice Cream<span>$12</span></a></li>
                  <li><a class="nn"><i class="fa fa-check" aria-hidden="true"></i>Mango Ice Cream<span>$8</span></a></li>
                  <li><a class="nn"><i class="fa fa-check" aria-hidden="true"></i>Tropical Fruit Ice Cream<span>$5</span></a></li>
                  <li><a class="nn"><i class="fa fa-check" aria-hidden="true"></i>Chocolate Ice Cream<span>$17</span></a></li>
                  <li><a class="nn"><i class="fa fa-check" aria-hidden="true"></i>Vegan Ice Cream<span>$5</span></a></li>
                </ul>
              </div>
            </b:includable>
          </b:widget>
        </b:section>
      </div>
      <div class="clearfix"></div>
    </div>
  </div>

  <!-- Intro Author Section -->
  <div id="intro-author-wrap">
    <b:section class="wow jackInTheBox" id="intro-author-heading" maxwidgets="1" name="Headline Text 02" showaddelement="yes">
      <b:widget id='HTML1' locked='false' title='Find out more about what we do' type='HTML' version='2' visible='true'>
        <b:widget-settings>
          <b:widget-setting name='content'><![CDATA[
            <div class="container row">
              <div class="author-content">
                <style type="text/css">
                  #intro-author-wrap{display:block}
                </style>
                <h3 class="author-title">Find out more about what we do</h3>
                <p class="author-snippet">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English.
                Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since. Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since.
                </p>
                <div class="small-button">
                  <a class="button medium" href="#">Learn Methods</a>
                </div>
              </div>
            </div>
          ]]></b:widget-setting>
        </b:widget-settings>
        <b:includable id='main'>
          <div class='widget-content'>
            <data:content/>
          </div>
        </b:includable>
      </b:widget>
    </b:section>
    <div class="clearfix"></div>
  </div>

    <!-- Content Wrapper -->
    <div id='content-wrapper'>
      <div class='container row'>
    <!-- Main Wrapper -->
    <div id='main-wrapper'>

        <b:if cond='data:view.isMultipleItems'>
        <b:section class='wow bounceInUp head-text' id='head-text1' maxwidgets='10' name='Headline Text 05' showaddelement='yes'>
          <b:widget id='Text1' locked='true' title='Letest Product' type='Text' version='2' visible='true'>
            <b:widget-settings>
              <b:widget-setting name='content'><![CDATA[Lorem Ipsum has been the industry's standard dummy text.]]></b:widget-setting>
            </b:widget-settings>
            <b:includable id='main'>
              <b:include name='widget-title'/>
              <p class='widget-content'><data:content/></p>
            </b:includable>
          </b:widget>
        </b:section>
        <div class='clearfix'/>
      </b:if>

              <b:if cond='data:skin.vars.enablePremium == &quot;true&quot;'>
        <b:section class='main-widget-top' id='main-widget-top' maxwidgets='5' name='Main Widgets (Top)' showaddelement='yes'/>
      </b:if>

      <b:section class='main' id='main' maxwidgets='1' name='Main Posts' showaddelement='yes'>
        <b:widget id='Blog1' locked='true' title='Blog Posts' type='Blog' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='showDateHeader'>false</b:widget-setting>
            <b:widget-setting name='commentLabel'>Comments</b:widget-setting>
            <b:widget-setting name='style.textcolor'>#ffffff</b:widget-setting>
            <b:widget-setting name='showShareButtons'>false</b:widget-setting>
            <b:widget-setting name='authorLabel'>by</b:widget-setting>
            <b:widget-setting name='showCommentLink'>true</b:widget-setting>
            <b:widget-setting name='style.urlcolor'>#ffffff</b:widget-setting>
            <b:widget-setting name='showAuthor'>true</b:widget-setting>
            <b:widget-setting name='style.linkcolor'>#ffffff</b:widget-setting>
            <b:widget-setting name='style.unittype'>TextAndImage</b:widget-setting>
            <b:widget-setting name='style.bgcolor'>#ffffff</b:widget-setting>
            <b:widget-setting name='timestampLabel'/>
            <b:widget-setting name='reactionsLabel'/>
            <b:widget-setting name='showAuthorProfile'>true</b:widget-setting>
            <b:widget-setting name='style.layout'>1x1</b:widget-setting>
            <b:widget-setting name='showLabels'>true</b:widget-setting>
            <b:widget-setting name='showLocation'>false</b:widget-setting>
            <b:widget-setting name='postLabelsLabel'>Tags:</b:widget-setting>
            <b:widget-setting name='showTimestamp'>true</b:widget-setting>
            <b:widget-setting name='postsPerAd'>1</b:widget-setting>
            <b:widget-setting name='showBacklinks'>false</b:widget-setting>
            <b:widget-setting name='style.bordercolor'>#ffffff</b:widget-setting>
            <b:widget-setting name='showInlineAds'>false</b:widget-setting>
            <b:widget-setting name='showReactions'>false</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main' var='this'>
            <b:include name='searchMessage'/>
            <div class='blog-posts hfeed'>
              <b:class cond='data:view.isMultipleItems' name='index-post-wrap'/>
              <b:class cond='data:view.isSingleItem' name='item-post-wrap'/>
            <b:tag class='grid-posts' cond='data:view.isMultipleItems' name='div'>
                <b:loop index='i' values='data:posts' var='post'>
                  <b:include data='post' name='postCommentsAndAd'/>
                </b:loop>
              </b:tag>
            </div>
            <b:include cond='data:view.isMultipleItems' name='postPagination'/>
            <b:include name='feedLinks'/>
          </b:includable>
          <b:includable id='aboutPostAuthor'>
            <b:comment>Disabled</b:comment>  
          </b:includable>
          <b:includable id='addComments'>
            <a expr:href='data:post.commentsUrl' expr:onclick='data:post.commentsUrlOnclick'>
              <b:message name='messages.postAComment'/>
            </a>
          </b:includable>
          <b:includable id='backLinks' var='post'>
            <b:comment>Disabled</b:comment>     
          </b:includable>
          <b:includable id='blogThisShare'>
        <b:with value='&quot;window.open(this.href, \&quot;_blank\&quot;, \&quot;height=270,width=475\&quot;); return false;&quot;' var='onclick'>
          <b:include name='platformShare'/>
        </b:with>
      </b:includable>
                  <b:includable id='bylineByName' var='byline'>
        <b:switch var='data:byline.name'>
        <b:case value='share'/>
          <b:include cond='data:post.shareUrl' name='postShareButtons'/>
        <b:case value='comments'/>
          <b:include cond='data:post.allowComments' name='postCommentsLink'/>
        <b:case value='location'/>
          <b:include cond='data:post.location' name='postLocation'/>
        <b:case value='timestamp'/>
          <b:include cond='not data:view.isPage' name='postTimestamp'/>
        <b:case value='author'/>
          <b:include name='postAuthor'/>
        <b:case value='labels'/>
          <b:include cond='data:post.labels' name='postLabels'/>
        <b:case value='icons'/>
          <b:include cond='data:post.emailPostUrl' name='emailPostIcon'/>
        <b:case value='reactions'/>
          <b:include cond='data:post.reactionsUrl' name='postReactions'/>
        </b:switch>
      </b:includable>
                  <b:includable id='bylineRegion' var='regionItems'>
        <b:loop values='data:regionItems' var='byline'>
          <b:include data='byline' name='bylineByName'/>
        </b:loop>
      </b:includable>
                  <b:includable id='commentAuthorAvatar'>
                    <div class='avatar-image-container'>
                      <img class='author-avatar' expr:src='data:comment.authorAvatarSrc' height='45' width='45'/>
                    </div>
                  </b:includable>
                  <b:includable id='commentDeleteIcon' var='comment'>
                    <span expr:class='&quot;item-control &quot; + data:comment.adminClass'>
                      <b:if cond='data:showCmtPopup'>
                        <div class='goog-toggle-button'>
                          <div class='goog-inline-block comment-action-icon'/>
                        </div>
                        <b:else/>
                        <a class='comment-delete' expr:href='data:comment.deleteUrl' expr:title='data:messages.deleteComment'>
                          <img src='https://resources.blogblog.com/img/icon_delete13.gif'/>
                        </a>
                      </b:if>
                    </span>
                  </b:includable>
                  <b:includable id='commentForm' var='post'>
                    <div class='comment-form'>
                      <a name='comment-form'/>
                      <b:if cond='data:this.messages.blogComment != &quot;&quot;'>
                        <p><data:this.messages.blogComment/></p>
                      </b:if>
                      <b:include data='post' name='commentFormIframeSrc'/>
                      <iframe allowtransparency='allowtransparency' class='blogger-iframe-colorize blogger-comment-from-post' expr:height='data:cmtIframeInitialHeight ?: &quot;90px&quot;' frameborder='0' id='comment-editor' name='comment-editor' src='' width='100%'/>
                      <data:post.cmtfpIframe/>
                      <script type='text/javascript'>
                        BLOG_CMT_createIframe(&#39;<data:post.appRpcRelayPath/>&#39;);
                      </script>
                    </div>
                  </b:includable>
                  <b:includable id='commentFormIframeSrc' var='post'>
                    <a expr:href='data:post.commentFormIframeSrc + &quot;&amp;skin=contempo&quot;' id='comment-editor-src'/>
                  </b:includable>
                  <b:includable id='commentItem' var='comment'>
                    <div class='comment' expr:id='&quot;c&quot; + data:comment.id'>
                      <b:include cond='data:blog.enabledCommentProfileImages' name='commentAuthorAvatar'/>

                      <div class='comment-block'>
                        <div class='comment-author'>
                          <b:if cond='data:comment.authorUrl'>
                            <b:message name='messages.authorSaidWithLink'>
                              <b:param expr:value='data:comment.author' name='authorName'/>
                              <b:param expr:value='data:comment.authorUrl' name='authorUrl'/>
                            </b:message>
                            <b:else/>
                            <b:message name='messages.authorSaid'>
                              <b:param expr:value='data:comment.author' name='authorName'/>
                            </b:message>
                          </b:if>
                        </div>
                        <div expr:class='&quot;comment-body&quot; + (data:comment.isDeleted ? &quot; deleted&quot; : &quot;&quot;)'>
                          <data:comment.body/>
                        </div>
                        <div class='comment-footer'>
                          <span class='comment-timestamp'>
                            <a expr:href='data:comment.url' title='comment permalink'>
                              <data:comment.timestamp/>
                            </a>
                            <b:include data='comment' name='commentDeleteIcon'/>
                          </span>
                        </div>
                      </div>
                    </div>
                  </b:includable>
                  <b:includable id='commentList' var='comments'>
                    <div id='comments-block'>
                      <b:loop values='data:comments' var='comment'>
                        <b:include data='comment' name='commentItem'/>
                      </b:loop>
                    </div>
                  </b:includable>
                  <b:includable id='commentPicker' var='post'>
                    <b:if cond='data:post.commentSource == 1'>
                      <b:include data='post' name='iframeComments'/>
                      <b:elseif cond='data:post.showThreadedComments'/>
                      <b:include data='post' name='threadedComments'/>
                      <b:else/>
                      <b:include data='post' name='comments'/>
                    </b:if>
                  </b:includable>
                  <b:includable id='comments' var='post'>
                    <section expr:class='&quot;comments&quot; + (data:post.embedCommentForm ? &quot; embed&quot; : &quot;&quot;)' expr:data-num-comments='data:post.numberOfComments' id='comments'>
                      <a name='comments'/>
                      <b:if cond='data:post.allowComments'>

                        <b:include name='commentsTitle'/>

                        <div expr:id='data:widget.instanceId + &quot;_comments-block-wrapper&quot;'>
                          <b:include cond='data:post.comments' data='post.comments' name='commentList'/>
                        </div>

                        <b:if cond='data:post.commentPagingRequired'>
                          <div class='paging-control-container'>
                            <b:if cond='data:post.hasOlderLinks'>
                              <a expr:class='data:post.oldLinkClass' expr:href='data:post.oldestLinkUrl'>
                                <data:messages.oldest/>
                              </a>
                              <a expr:class='data:post.oldLinkClass' expr:href='data:post.olderLinkUrl'>
                                <data:messages.older/>
                              </a>
                            </b:if>

                            <span class='comment-range-text'>
                              <data:post.commentRangeText/>
                            </span>

                            <b:if cond='data:post.hasNewerLinks'>
                              <a expr:class='data:post.newLinkClass' expr:href='data:post.newerLinkUrl'>
                                <data:messages.newer/>
                              </a>
                              <a expr:class='data:post.newLinkClass' expr:href='data:post.newestLinkUrl'>
                                <data:messages.newest/>
                              </a>
                            </b:if>
                          </div>
                        </b:if>

                        <div class='footer'>
                          <b:if cond='data:post.embedCommentForm'>
                            <b:if cond='data:post.allowNewComments'>
                              <b:include data='post' name='commentForm'/>
                              <b:else/>
                              <data:post.noNewCommentsText/>
                            </b:if>
                            <b:else/>
                            <b:if cond='data:post.allowComments'>
                              <b:include data='post' name='addComments'/>
                            </b:if>
                          </b:if>
                        </div>
                      </b:if>
                      <b:if cond='data:showCmtPopup'>
                        <div id='comment-popup'>
                          <iframe allowtransparency='allowtransparency' frameborder='0' id='comment-actions' name='comment-actions' scrolling='no'>
                          </iframe>
                        </div>
                      </b:if>
                    </section>
                  </b:includable>
                  <b:includable id='commentsLink'>
        <a class='comment-link' expr:href='data:post.commentsUrl' expr:onclick='data:post.commentsUrlOnclick'>
          <b:if cond='data:post.numberOfComments &gt; 0'>
            <b:message name='messages.numberOfComments'>
              <b:param expr:value='data:post.numberOfComments' name='numComments'/>
            </b:message>
          <b:else/>
            <data:messages.postAComment/>
          </b:if>
        </a>
      </b:includable>
    <b:includable id='commentsLinkIframe'>
  <!-- G+ comments, no longer available. The includable is retained for backwards-compatibility. -->
</b:includable>
            <b:includable id='commentsTitle'>
              <!-- Post Commments Title -->
              <h3 class='title'><data:post.numberOfComments/> <data:messages.comments/></h3>
              <b:class cond='data:post.numberOfComments == &quot;0&quot;' name='no-comments'/>
            </b:includable>
            <b:includable id='defaultAdUnit'>
  <ins class='adsbygoogle' data-ad-format='auto' expr:data-ad-client='data:adClientId ?: data:blog.adsenseClientId' expr:data-ad-host='data:blog.adsenseHostId' expr:data-analytics-uacct='data:blog.analyticsAccountNumber' expr:style='data:style ?: &quot;display: block;&quot;'/>
  <script>
   (adsbygoogle = window.adsbygoogle || []).push({});
  </script>
</b:includable>
            <b:includable id='emailPostIcon'>
  <span class='byline post-icons'>
    <!-- email post links -->
    <span class='item-action'>
      <a expr:href='data:post.emailPostUrl' expr:title='data:messages.emailPost'>
        <b:include data='{ iconClass: &quot;touch-icon sharing-icon&quot; }' name='emailIcon'/>
      </a>
    </span>
  </span>
</b:includable>
            <b:includable id='facebookShare'>
  <b:with value='&quot;window.open(this.href, \&quot;_blank\&quot;, \&quot;height=430,width=640\&quot;); return false;&quot;' var='onclick'>
    <b:include name='platformShare'/>
  </b:with>
</b:includable>
            <b:includable id='feedLinks'>
              <b:comment>Disabled</b:comment> 
            </b:includable>
            <b:includable id='feedLinksBody' var='links'>
              <b:comment>Disabled</b:comment> 
            </b:includable>
            <b:includable id='footerBylines' var='post'>
              <!-- Post Footer Extras -->
              <b:include data='post' name='postLabels'/>
              <b:include data='post' name='postShareButtons'/>
            </b:includable>
            <b:includable id='googlePlusShare'>
  <div class='goog-inline-block google-plus-share-container'>
    <g:plusone annotation='inline' expr:href='data:originalUrl.canonical.http' size='medium' source='blogger:blog:plusone'/>
  </div>
</b:includable>
            <b:includable id='headerByline' var='post'>
              <!-- Post Header Meta -->
                <div class='post-meta'>
<b:include data='post' name='postAuthor'/>
                  <b:include data='post' name='postTimestamp'/>
                  
                </div>
            </b:includable>
            <b:includable id='homePageLink'>
              <b:comment>Disabled</b:comment> 
            </b:includable>
            <b:includable id='iframeComments' var='post'>
              <b:if cond='data:post.allowIframeComments'>
                <script expr:src='data:post.iframeCommentSrc' type='text/javascript'/>
                <div class='cmt_iframe_holder' expr:data-href='data:post.url.canonical' expr:data-viewtype='data:post.viewType'/>
                <b:if cond='!data:post.embedCommentForm'>
                  <b:include data='post' name='commentsLink'/>
                </b:if>
              </b:if>
            </b:includable>
            <b:includable id='indexPost' var='post'>
              <!-- Index Post Content -->
              <b:include data='post' name='postFeaturedImage'/>
    <div class='post-content'>
                    <div class='post-info'>
                      <b:include data='post' name='postTitle'/>
<div class='clearfix'/>
 <div class='post-meta'>
                        <b:include data='post' name='postAuthor'/>    <b:include data='post' name='postTimestamp'/>
 <b:include data='post' name='postCategory'/>
                      </div>
<div class='clearfix'/>
                   <b:include data='post' name='postSummary'/>
                    </div>
                  </div>
            </b:includable>
            <b:includable id='inlineAd' var='post'>
              <b:comment>Disabled</b:comment>
            </b:includable>
            <b:includable id='itemPost' var='post'>
              <!-- Item Post Content -->
              <b:include data='post' name='postMeta'/>
              <div class='post-header'>
                <b:include data='post' name='postHeader'/>
              </div>
              <b:include data='post' name='postBody'/>
              <b:include cond='data:view.isPost' data='post' name='postFooter'/>
            </b:includable>
            <b:includable id='linkShare'>
  <b:with value='&quot;window.prompt(\&quot;Copy to clipboard: Ctrl+C, Enter\&quot;, \&quot;&quot; + data:originalUrl + &quot;\&quot;); return false;&quot;' var='onclick'>
    <b:include name='platformShare'/>
  </b:with>
</b:includable>
            <b:includable id='manageComments'>
  <a expr:href='data:post.manageCommentsUrl' expr:onclick='data:post.manageCommentsUrlOnclick'>
    <b:message name='messages.manageComments'/>
  </a>
</b:includable>
            <b:includable id='nextPageLink'>
              <a class='blog-pager-older-link' expr:href='data:olderPageUrl' expr:id='data:widget.instanceId + &quot;_blog-pager-older-link&quot;' expr:title='data:messages.olderPosts'>
                <data:messages.showMore/>
              </a>
            </b:includable>
            <b:includable id='otherSharingButton'>
  <span class='sharing-platform-button sharing-element-other' expr:aria-label='data:messages.shareToOtherApps.escaped' expr:data-url='data:originalUrl' expr:title='data:messages.shareToOtherApps.escaped' role='menuitem' tabindex='-1'>
    <b:with value='{key: &quot;sharingOther&quot;}' var='platform'>
      <b:include name='sharingPlatformIcon'/>
    </b:with>
    <span class='platform-sharing-text'><data:messages.shareOtherApps.escaped/></span>
  </span>
</b:includable>
            <b:includable id='platformShare'>
  <a expr:class='&quot;goog-inline-block sharing-&quot; + data:platform.key' expr:data-url='data:originalUrl' expr:href='data:shareUrl + &quot;&amp;target=&quot; + data:platform.target' expr:onclick='data:onclick ? data:onclick : &quot;&quot;' expr:title='data:platform.shareMessage' target='_blank'>
    <span class='share-button-link-text'>
      <data:platform.shareMessage/>
    </span>
  </a>
</b:includable>
            <b:includable id='post' var='post'>
              <!-- Post Index -->
              <b:include cond='data:view.isMultipleItems' data='post' name='indexPost'/>
              <!-- Post Item -->
              <b:include cond='data:view.isSingleItem' data='post' name='itemPost'/>
            </b:includable>
            <b:includable id='postAuthor' var='post'>
                <!-- Post Author -->
              <b:if cond='data:allBylineItems.author'>
                <span class='post-author'><a expr:href='data:post.author.profileUrl' expr:title='data:post.author.name' target='_blank'><data:post.author.name/></a></span>
              </b:if>  
            </b:includable>
            <b:includable id='postBody' var='post'> 
              <!-- Post Body Entry Content-->
              <div class='post-body post-content' id='post-body'>
                <data:post.body/>
              </div>
            </b:includable>
            <b:includable id='postBodySnippet' var='post'>
              <b:include data='post' name='postBody'/>
            </b:includable>
            <b:includable id='postBreadcrumbs' var='post'>
              <!-- Post Breadcrumbs -->
              <script type='application/ld+json'>
              {
                &quot;@context&quot;: &quot;http://schema.org&quot;,
                &quot;@type&quot;: &quot;BreadcrumbList&quot;,
                &quot;itemListElement&quot;: [{
                  &quot;@type&quot;: &quot;ListItem&quot;,
                  &quot;position&quot;: 1,
                  &quot;item&quot;: {
                    &quot;name&quot;: &quot;<data:messages.home/>&quot;,
                    &quot;@id&quot;: &quot;<data:blog.homepageUrl.jsonEscaped/>&quot;
                  }
                },{
                  &quot;@type&quot;: &quot;ListItem&quot;,
                  &quot;position&quot;: 2,
                  &quot;item&quot;: {
                    &quot;name&quot;: &quot;<b:if cond='data:post.labels'><data:post.labels.first.name/></b:if>&quot;,
                    &quot;@id&quot;: &quot;<data:post.labels.last.url.jsonEscaped/>&quot;
                  }
                },{
                  &quot;@type&quot;: &quot;ListItem&quot;,
                  &quot;position&quot;: 3,
                  &quot;item&quot;: {
                    &quot;name&quot;: &quot;<data:post.title/>&quot;,
                    &quot;@id&quot;: &quot;<data:post.url.jsonEscaped/>&quot;
                  }
                }]
              }
            </script>
            </b:includable>
            <b:includable id='postCategory' var='post'>
              <!-- Post Label/Category -->
              <b:if cond='data:view.isMultipleItems and data:post.labels'>
                <span class='post-tag index-post-tag'>
                  <data:post.labels.last.name/>
                </span>
              </b:if>
            </b:includable>
            <b:includable id='postCommentsAndAd' var='post'>
              <!-- Post, Comments and Ads -->
              <!-- Post Content Index and Item -->
              <div class='blog-post hentry'>
                <b:class cond='data:view.isMultipleItems' expr:name='&quot;index-post wow zoomIn post-&quot; + data:i'/>
                <b:class cond='data:view.isSingleItem' name='item-post'/>
                <b:include data='post' name='post'/>
              </div>
              <!-- Comments -->
              <b:if cond='data:view.isSingleItem and data:post.allowComments'>
                <div class='blog-post-comments'>
                  <b:include data='post' name='threadedCommentsDisqus'/>
                  <b:include data='post' name='commentPicker'/>
                </div>
              </b:if>
              <b:include cond='data:view.isPost and data:allBylineItems.backlinks' data='post' name='postNavigation'/> 
            </b:includable>
            <b:includable id='postCommentsLink'>
              <b:if cond='data:view.isMultipleItems'>
                <span class='byline post-comment-link container'>
                  <b:include cond='data:post.commentSource != 1' name='commentsLink'/>
                  <b:include cond='data:post.commentSource == 1' name='commentsLinkIframe'/>
                </span>
              </b:if>
            </b:includable>
            <b:includable id='postFeaturedImage' var='post'>
              <!-- Post Featured Image on Index -->
              <div class='post-image-wrap'>
                <a class='post-image-link' expr:href='data:post.url'>
                  <b:if cond='data:post.featuredImage'> 
                    <img class='post-thumb' expr:alt='data:post.title' expr:src='data:post.featuredImage.isYouTube ? resizeImage(data:post.featuredImage.youtubeMaxResDefaultUrl.jsonEscaped, 72, &quot;1:1&quot;) : resizeImage(data:post.featuredImage, 72, &quot;1:1&quot;)'/>
                    <b:else/>
                    <img class='post-thumb' expr:alt='data:post.title' src='https://4.bp.blogspot.com/-O3EpVMWcoKw/WxY6-6I4--I/AAAAAAAAB2s/KzC0FqUQtkMdw7VzT6oOR_8vbZO6EJc-ACK4BGAYYCw/w480/nth.png'/>
                  </b:if>
                </a>
              </div>
            </b:includable>
            <b:includable id='postFooter' var='post'>
              <!-- Post Footer Itens -->
              <div class='post-footer'>
                <!-- Post Labels and Share Buttons -->
                <b:include data='post' name='footerBylines'/>
                <b:include data='post' name='postRelated'/>
              </div>
            </b:includable>
            <b:includable id='postFooterAuthorProfile' var='post'>
              <b:comment>Disabled</b:comment>  
            </b:includable>
            <b:includable id='postHeader' var='post'>
              <b:include cond='data:view.isPost' data='post' name='postBreadcrumbs'/>
              <b:include data='post' name='postTitle'/>
              <b:include cond='!data:view.isPage' data='post' name='headerByline'/>
            </b:includable>
            <b:includable id='postJumpLink' var='post'>
              <b:comment>Disabled</b:comment>  
            </b:includable>
            <b:includable id='postLabels' var='post'>
              <b:if cond='data:allBylineItems.labels'>
                <b:if cond='data:post.labels'>
                  <div class='post-labels'>
                      <b:loop values='data:post.labels' var='label'>
                        <span class='Label'><a class='label-link' expr:href='data:label.url' rel='tag'><data:label.name/></a></span>
                      </b:loop>
                  </div>
                </b:if>
              </b:if>
            </b:includable>
            <b:includable id='postLocation'>
  <span class='byline post-location'>
    <data:byline.label/>
    <a expr:href='data:post.location.mapsUrl' target='_blank'><data:post.location.name/></a>
  </span>
</b:includable>
            <b:includable id='postMeta' var='post'>
              <b:include data='post' name='postMetadataJSON'/>
            </b:includable>
            <b:includable id='postMetadataJSONImage'>
  &quot;image&quot;: {
    &quot;@type&quot;: &quot;ImageObject&quot;,
    <b:if cond='data:post.featuredImage.isResizable'>
    &quot;url&quot;: &quot;<b:eval expr='resizeImage(data:post.featuredImage, 1200, &quot;1200:630&quot;)'/>&quot;,
    &quot;height&quot;: 630,
    &quot;width&quot;: 1200
    <b:else/>
    &quot;url&quot;: &quot;https://lh3.googleusercontent.com/ULB6iBuCeTVvSjjjU1A-O8e9ZpVba6uvyhtiWRti_rBAs9yMYOFBujxriJRZ-A=w1200&quot;,
    &quot;height&quot;: 348,
    &quot;width&quot;: 1200
    </b:if>
  },
</b:includable>
            <b:includable id='postMetadataJSONPublisher'>
 &quot;publisher&quot;: {
    &quot;@type&quot;: &quot;Organization&quot;,
    &quot;name&quot;: &quot;Blogger&quot;,
    &quot;logo&quot;: {
      &quot;@type&quot;: &quot;ImageObject&quot;,
      &quot;url&quot;: &quot;https://lh3.googleusercontent.com/ULB6iBuCeTVvSjjjU1A-O8e9ZpVba6uvyhtiWRti_rBAs9yMYOFBujxriJRZ-A=h60&quot;,
      &quot;width&quot;: 206,
      &quot;height&quot;: 60
    }
  },
</b:includable>
            <b:includable id='postNavigation' var='post'>
              <!-- Post Navigation Item -->
              <div class='post-nav'>
                <b:if cond='data:newerPageUrl'> 
                  <a class='next-post-link nav-link' expr:href='data:newerPageUrl' expr:id='data:widget.instanceId + &quot;_blog-pager-newer-link&quot;' rel='next'><data:messages.newer/></a> 
                  <b:else/> 
                  <span class='next-post-link nav-link'><data:messages.newest/></span> 
                </b:if> 
                <b:if cond='data:olderPageUrl'> 
                  <a class='prev-post-link nav-link' expr:href='data:olderPageUrl' expr:id='data:widget.instanceId + &quot;_blog-pager-older-link&quot;' rel='previous'><data:messages.older/></a> 
                  <b:else/>
                  <span class='prev-post-link nav-link'><data:messages.oldest/></span> 
                </b:if> 
              </div>
            </b:includable>
            <b:includable id='postPagination'>
              <div class='blog-pager' id='blog-pager'>
                <b:include cond='data:olderPageUrl' name='nextPageLink'/>
              </div>
            </b:includable>
            <b:includable id='postReactions'>
  <span class='byline reactions'>
    <span class='reactions-label'>
      <data:byline.label/>
    </span>
    <iframe allowtransparency='true' class='reactions-iframe' expr:src='data:post.reactionsUrl' frameborder='0' name='reactions' scrolling='no'/>
  </span>
</b:includable>
            <b:includable id='postRelated' var='post'>
              <!-- Related Posts -->
                <div id='related-wrap'>
                  <div class='title-wrap'>
                    <h3><data:messages.youMayLikeThesePosts/></h3>
                  </div>
                  <div class='related-ready'>
                    <b:if cond='data:post.labels'>
                      <div class='related-tag' expr:data-label='data:post.labels.first.name'/>
                      <b:else/>
                      <div class='related-tag' data-label='random'/>
                    </b:if>
                  </div> 
                </div>  
            </b:includable>
            <b:includable id='postShareButtons' var='post'>
              <!-- Post ShareButtons -->
              <b:if cond='data:allBylineItems.share'>
                <div class='post-share'>
                  <ul class='share-links social social-color'>
                    <b:class cond='data:blog.isMobileRequest' name='is-mobile'/>
                    <li class='facebook-f'><a class='facebook' expr:href='&quot;https://www.facebook.com/sharer.php?u=&quot; + data:post.url' onclick='window.open(this.href, &apos;windowName&apos;, &apos;width=550, height=650, left=24, top=24, scrollbars, resizable&apos;); return false;' rel='nofollow'/></li>
                    <li class='twitter'><a class='twitter' expr:href='&quot;https://twitter.com/intent/tweet?via=templatesyard&amp;url=&quot; + data:post.url + &quot;&amp;text=&quot; + data:post.title' onclick='window.open(this.href, &apos;windowName&apos;, &apos;width=550, height=450, left=24, top=24, scrollbars, resizable&apos;); return false;' rel='nofollow'/></li>
                    <li class='pinterest-p'><a class='pinterest' expr:href='&quot;https://www.pinterest.com/pin/create/button/?url=&quot; + data:post.url + &quot;&amp;media=&quot; + data:post.featuredImage + &quot;&amp;description=&quot; + data:post.title' onclick='window.open(this.href, &apos;windowName&apos;, &apos;width=735, height=750, left=24, top=24, scrollbars, resizable&apos;); return false;' rel='nofollow'/></li> 
                                  <li class='linkedin'><a class='linkedin' expr:href='&quot;https://www.linkedin.com/shareArticle?url=&quot; + data:post.url' onclick='window.open(this.href, &apos;windowName&apos;, &apos;width=950, height=650, left=24, top=24, scrollbars, resizable&apos;); return false;' rel='nofollow'/></li>
                    <li class='whatsapp whatsapp-desktop'><a class='whatsapp' expr:href='&quot;https://web.whatsapp.com/send?text=&quot; + data:post.title + &quot; | &quot; + data:post.url' onclick='window.open(this.href, &apos;windowName&apos;, &apos;width=900, height=550, left=24, top=24, scrollbars, resizable&apos;); return false;' rel='nofollow'/></li>
                    <li class='whatsapp whatsapp-mobile'><a class='whatsapp' expr:href='&quot;https://api.whatsapp.com/send?text=&quot; + data:post.title + &quot; | &quot; + data:post.url' rel='nofollow' target='_blank'/></li> 
                    <li class='email'><a class='email' expr:href='&quot;mailto:?subject=&quot; + data:post.title + &quot;&amp;body=&quot; + data:post.url' onclick='window.open(this.href, &apos;windowName&apos;, &apos;width=500, height=400, left=24, top=24, scrollbars, resizable&apos;); return false;' rel='nofollow'/></li>
                  </ul>
                </div>
              </b:if>
            </b:includable>
            <b:includable id='postShortMeta'>
              <b:comment>Disabled</b:comment> 
            </b:includable>
            <b:includable id='postSummary' var='post'>
              <!-- Post Summary -->
              <p class='post-snippet'><b:eval expr='data:post.snippets.long snippet { length: 150 }'/></p>
            </b:includable>
            <b:includable id='postTimestamp' var='post'>
              <!-- Post Timestamp -->
              <b:if cond='data:allBylineItems.timestamp'>
                <span class='post-date published' expr:datetime='data:post.date.iso8601'><abbr class='published timeago' expr:title='data:post.date.iso8601' itemprop='datePublished'><data:post.date/></abbr></span>
              </b:if>
            </b:includable>
            <b:includable id='postTitle' var='post'>
              <!-- Post Title Index and Item -->
              <b:if cond='data:view.isMultipleItems'>
                <h2 class='post-title'>
                <a expr:href='data:post.url'><data:post.title/></a>
                </h2>
              </b:if>
              <b:if cond='data:view.isSingleItem'>
                <h1 class='post-title'>
                  <data:post.title/>
                </h1>
              </b:if>
            </b:includable>
            <b:includable id='previousPageLink'>
              <b:comment>Disabled</b:comment> 
            </b:includable>
            <b:includable id='searchMessage'>
              <!-- Search Message -->
              <b:if cond='data:view.search.query'>
                <div class='queryMessage'>
                  <b:if cond='data:posts.empty'>
                    <span class='query-info query-error'/><data:view.search.resultsMessageHtml/><a class='show-more' expr:href='data:blog.homepageUrl'><data:messages.showAll/></a>
                    <b:else/>
                    <span class='query-info query-success'><data:view.search.resultsMessageHtml/></span><a class='show-more' expr:href='data:blog.homepageUrl'><data:messages.showAll/></a>
                  </b:if>
                </div>
              </b:if>
              <b:if cond='data:view.search.label'>
                <div class='queryMessage'>
                  <b:if cond='data:posts.empty'>
                    <span class='query-info query-error'><data:view.search.resultsMessageHtml/></span><a class='show-more' expr:href='data:blog.homepageUrl'><data:messages.showAll/></a>
                    <b:else/>
                    <span class='query-info query-success'><data:view.search.resultsMessageHtml/></span><a class='show-more' expr:href='data:blog.homepageUrl'><data:messages.showAll/></a>
                  </b:if>
                </div>
              </b:if>
              <b:if cond='data:view.isArchive'>
                <div class='queryMessage'>
                  <b:if cond='data:posts.empty'>
                    <span class='query-info query-error'><data:view.archive.rangeMessage/></span><a class='show-more' expr:href='data:blog.homepageUrl'><data:messages.showAll/></a>
                    <b:else/>
                    <span class='query-info query-success'><data:view.archive.rangeMessage/></span><a class='show-more' expr:href='data:blog.homepageUrl'><data:messages.showAll/></a>
                  </b:if>
                </div>
              </b:if>
              <b:if cond='data:view.isError'>
                <div class='errorWrap'>
                  <h3>404</h3>
                  <h4><data:messages.theresNothingHere/></h4>
                  <p><data:navMessage/></p>
                  <a class='homepage' expr:href='data:blog.homepageUrl'><i class='fa fa-home'/> <data:messages.home/></a>
                </div>
              </b:if>
              <b:if cond='data:view.isMultipleItems and data:posts.empty'><div class='queryEmpty'><data:messages.noResultsFound/></div></b:if>
            </b:includable>
            <b:includable id='sharingButton'>
  <span expr:aria-label='data:platform.shareMessage' expr:class='&quot;sharing-platform-button sharing-element-&quot; + data:platform.key' expr:data-href='data:shareUrl + &quot;&amp;target=&quot; + data:platform.target' expr:data-url='data:originalUrl' expr:title='data:platform.shareMessage' role='menuitem' tabindex='-1'>
    <b:include name='sharingPlatformIcon'/>
    <span class='platform-sharing-text'><data:platform.name/></span>
  </span>
</b:includable>
            <b:includable id='sharingButtonContent'>
  <div class='flat-icon-button ripple'>
    <b:include name='shareIcon'/>
  </div>
</b:includable>
            <b:includable id='sharingButtons'>
  <div class='sharing' expr:aria-owns='&quot;sharing-popup-&quot; + data:sharingId' expr:data-title='data:shareTitle'>
    <button class='sharing-button touch-icon-button' expr:aria-controls='&quot;sharing-popup-&quot; + data:sharingId' expr:aria-label='data:messages.share.escaped' expr:id='&quot;sharing-button-&quot; + data:sharingId' role='button'>
      <b:include name='sharingButtonContent'/>
    </button>
    <b:include name='sharingButtonsMenu'/>
  </div>
</b:includable>
            <b:includable id='sharingButtonsMenu'>
  <div class='share-buttons-container'>
    <ul aria-hidden='true' class='share-buttons hidden' expr:aria-label='data:messages.share.escaped' expr:id='&quot;sharing-popup-&quot; + data:sharingId' role='menu'>
      <b:loop values='(data:platforms ?: data:blog.sharing.platforms) filter (p =&gt; p.key not in {&quot;blogThis&quot;})' var='platform'>
        <li>
          <b:include name='sharingButton'/>
        </li>
      </b:loop>
      <li aria-hidden='true' class='hidden'>
        <b:include name='otherSharingButton'/>
      </li>
    </ul>
  </div>
</b:includable>
            <b:includable id='sharingPlatformIcon'>
  <b:include data='{ iconClass: (&quot;touch-icon sharing-&quot; + data:platform.key) }' expr:name='data:platform.key + &quot;Icon&quot;'/>
</b:includable>
            <b:includable id='threadedCommentForm' var='post'>
              <div class='comment-form'>
                <a name='comment-form'/>
                <b:if cond='data:this.messages.blogComment != &quot;&quot;'>
                  <p><data:this.messages.blogComment/></p>
                </b:if>
                <b:include data='post' name='commentFormIframeSrc'/>
                <iframe allowtransparency='allowtransparency' class='blogger-iframe-colorize blogger-comment-from-post' expr:height='data:cmtIframeInitialHeight ?: &quot;90px&quot;' frameborder='0' id='comment-editor' name='comment-editor' src='' width='100%'/>
                <data:post.cmtfpIframe/>
                <script type='text/javascript'>
                  BLOG_CMT_createIframe(&#39;<data:post.appRpcRelayPath/>&#39;);
                </script>
              </div>
            </b:includable>
            <b:includable id='threadedCommentJs' var='post'>
              <script async='async' expr:src='data:post.commentSrc' type='text/javascript'/>
              <b:template-script inline='true' name='threaded_comments'/>
              <script type='text/javascript'>
                blogger.widgets.blog.initThreadedComments(
                  <data:post.commentJso/>,
                  <data:post.commentMsgs/>,
                  <data:post.commentConfig/>);
              </script>
            </b:includable>
            <b:includable id='threadedComments' var='post'>
              <section class='comments threaded' expr:data-embed='data:post.embedCommentForm' expr:data-num-comments='data:post.numberOfComments' id='comments'>
                <a name='comments'/>
                <b:include name='commentsTitle'/>
                <div class='comments-content'>
                  <b:if cond='data:post.embedCommentForm'>
                    <b:include data='post' name='threadedCommentJs'/>
                  </b:if>
                  <div id='comment-holder'>
                    <data:post.commentHtml/>
                  </div>
                </div>
                <p class='comment-footer'>
                  <b:if cond='data:post.allowNewComments'>
                    <b:include data='post' name='threadedCommentForm'/>
                    <b:else/>
                    <data:post.noNewCommentsText/>
                  </b:if>
                </p>
                <b:if cond='data:showCmtPopup'>
                  <div id='comment-popup'>
                    <iframe allowtransparency='allowtransparency' frameborder='0' id='comment-actions' name='comment-actions' scrolling='no'>
                    </iframe>
                  </div>
                </b:if>
              </section>
            </b:includable>
            <b:includable id='threadedCommentsDisqus' var='post'>
              <script type='text/javascript'>
                var disqus_blogger_current_url = &quot;<data:blog.canonicalUrl/>&quot;;
                if (!disqus_blogger_current_url.length) {
                  disqus_blogger_current_url = &quot;<data:blog.url/>&quot;;
                }
                var disqus_blogger_homepage_url = &quot;<data:blog.homepageUrl/>&quot;;
                var disqus_blogger_canonical_homepage_url = &quot;<data:blog.canonicalHomepageUrl/>&quot;;
              </script>
            </b:includable>
            <b:includable id='tooltipCss'>
  <!-- LINT.IfChange -->
  <style>
    .post-body a.b-tooltip-container {
      position: relative;
      display: inline-block;
    }

    .post-body a.b-tooltip-container .b-tooltip {
      display: block !important;
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translate(-20%, 1px);
      visibility: hidden;
      opacity: 0;
      z-index: 1;
      transition: opacity 0.2s ease-in-out;
    }

    .post-body a.b-tooltip-container .b-tooltip iframe {
      width: 200px;
      height: 198px;
      max-width: none;
      border: none;
      border-radius: 20px;
      box-shadow: 1px 1px 3px 1px rgba(0, 0, 0, 0.2);
    }

    @media (hover: hover) {
      .post-body a.b-tooltip-container:hover .b-tooltip {
        visibility: visible;
        opacity: 1;
      }
    }
  </style>
  <!-- LINT.ThenChange(//depot/google3/java/com/google/blogger/b2/layouts/widgets/v2-style.css) -->
</b:includable>
          </b:widget>
        </b:section> 
      </div>
                  <!-- Sidebar has been removed -->
      </div>
  </div>

  <div class='clearfix'/>
  <!-- Footer Wrapper -->
  <div id='footer-wrapper'>
    <div class='wow bounceInUp' id='footer-copyright'>
    
      <div class='container row'>
     
      <div class='copyright-area'>Created By <a href='http://masarax.com/' id='mycontent' rel='dofollow' title='masarax'>MASARA X</a> | Distributed By <a href='/' rel='dofollow' style='color:#ffff00f2;' target='_blank' title='Gooyaabi'>عالم العطور</a>
    </div>  
   <b:section class='top-bar-nav' id='top-bar-nav' maxwidgets='1' name='Footer Navigation' showaddelement='yes'>
       <b:widget id='LinkList72' locked='true' title='Link List' type='LinkList' version='2' visible='true'>
         <b:widget-settings>
           <b:widget-setting name='sorting'>NONE</b:widget-setting>
           <b:widget-setting name='text-0'>Home</b:widget-setting>
           <b:widget-setting name='link-0'>/</b:widget-setting>
         </b:widget-settings>
         <b:includable id='main'>
              <b:include name='content'/>
            </b:includable>
         <b:includable id='content'>
              <div class='widget-content'>
                <ul>
                  <b:loop values='data:links' var='link'>
                    <li><a expr:href='data:link.target'><data:link.name/></a></li>
                  </b:loop>
                </ul> 
              </div>
            </b:includable>
       </b:widget>
     </b:section>
     <div class='clearfix'/> 
      </div>
    </div>
  </div>

  </div>
 <b:if cond='data:view.isSingleItem'>
<!-- Hidden Widgets -->
<div id='hidden-widgets-wrap' style='display:none'>
  <b:section class='hidden-widgets' deleted='true' id='hidden-widgets' name='Hidden Widgets' showaddelement='yes'>
    <b:widget id='ContactForm1' locked='false' title='Contact form' type='ContactForm' version='2' visible='true'>
      <b:includable id='main' var='this'>
      <b:include name='widget-title'/>
      <b:include name='content'/>
    </b:includable>
      <b:includable id='content'>
      <b:include name='contact-form-content'/>
    </b:includable>
    </b:widget>
  </b:section>
</div>
</b:if>
<!-- Hidden Search Tags Widget -->
<div style='display:none'>
  <b:section class='hidden-widgets' id='hidden-search-tags' maxwidgets='1' name='Hidden Search Tags' showaddelement='yes'>
    <b:widget id='Label99' locked='false' title='All Tags' type='Label' version='2' visible='true'>
      <b:widget-settings>
        <b:widget-setting name='sorting'>ALPHA</b:widget-setting>
        <b:widget-setting name='display'>LIST</b:widget-setting>
        <b:widget-setting name='selectedLabelsList'/>
        <b:widget-setting name='showType'>ALL</b:widget-setting>
        <b:widget-setting name='showFreqNumbers'>false</b:widget-setting>
      </b:widget-settings>
      <b:includable id='main'>
        <div class='widget-content'>
          <ul>
            <b:loop values='data:labels' var='label'>
              <li>
                <a expr:href='data:label.url'><data:label.name/> (<data:label.count/>)</a>
              </li>
            </b:loop>
          </ul>
        </div>
      </b:includable>
      <b:includable id='cloud'>
  <b:loop values='data:labels' var='label'>
    <span class='label-size'>
      <b:class expr:name='&quot;label-size-&quot; + data:label.cssSize'/>
      <a class='label-name' expr:href='data:label.url'>
        <data:label.name/>
        <b:if cond='data:this.showFreqNumbers'>
          <span class='label-count'><data:label.count/></span>
        </b:if>
      </a>
    </span>
  </b:loop>
</b:includable>
      <b:includable id='content'>
      <div class='widget-content'>
        <b:class expr:name='data:this.display + &quot;-label&quot;'/>
        <b:include cond='data:this.display == &quot;list&quot;' name='list'/>
        <b:include cond='data:this.display == &quot;cloud&quot;' name='list'/>
      </div>
    </b:includable>
      <b:includable id='list'>
      <ul>
        <b:loop values='data:labels' var='label'>
          <li>
            <a class='label-name' expr:href='data:label.url'>
              <data:label.name/>
              <b:if cond='data:this.showFreqNumbers'>
                <span class='label-count'>(<data:label.count/>)</span>
              </b:if>
            </a>
          </li>
        </b:loop>
      </ul>
    </b:includable>
    </b:widget>
  </b:section>
</div>
<!-- Main Scripts -->
<script src='https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js' type='text/javascript'/>

  <!-- Theme Functions JS -->
<script type='text/javascript'>
//<![CDATA[
/*
 * jQuery One Page Nav Plugin
 * http://github.com/davist11/jQuery-One-Page-Nav
 * Copyright (c) 2010 Trevor Davis (http://trevordavis.net)
 * Dual licensed under the MIT and GPL licenses.
 * Uses the same license as jQuery, see:
 * http://jquery.org/license
 * @version 3.0.0
 */
;(function($,window,document,undefined){var OnePageNav=function(elem,options){this.elem=elem;this.$elem=$(elem);this.options=options;this.metadata=this.$elem.data('plugin-options');this.$win=$(window);this.sections={};this.didScroll=false;this.$doc=$(document);this.docHeight=this.$doc.height();};OnePageNav.prototype={defaults:{navItems:'a',currentClass:'current',changeHash:false,easing:'swing',filter:'',scrollSpeed:750,scrollThreshold:0.5,begin:false,end:false,scrollChange:false},init:function(){this.config=$.extend({},this.defaults,this.options,this.metadata);this.$nav=this.$elem.find(this.config.navItems);if(this.config.filter!==''){this.$nav=this.$nav.filter(this.config.filter);}
this.$nav.on('click.onePageNav',$.proxy(this.handleClick,this));this.getPositions();this.bindInterval();this.$win.on('resize.onePageNav',$.proxy(this.getPositions,this));return this;},adjustNav:function(self,$parent){self.$elem.find('.'+self.config.currentClass).removeClass(self.config.currentClass);$parent.addClass(self.config.currentClass);},bindInterval:function(){var self=this;var docHeight;self.$win.on('scroll.onePageNav',function(){self.didScroll=true;});self.t=setInterval(function(){docHeight=self.$doc.height();if(self.didScroll){self.didScroll=false;self.scrollChange();}
if(docHeight!==self.docHeight){self.docHeight=docHeight;self.getPositions();}},250);},getHash:function($link){return $link.attr('href').split('#')[1];},getPositions:function(){var self=this;var linkHref;var topPos;var $target;self.$nav.each(function(){linkHref=self.getHash($(this));$target=$('#'+linkHref);if($target.length){topPos=$target.offset().top;self.sections[linkHref]=Math.round(topPos);}});},getSection:function(windowPos){var returnValue=null;var windowHeight=Math.round(this.$win.height()*this.config.scrollThreshold);for(var section in this.sections){if((this.sections[section]-windowHeight)<windowPos){returnValue=section;}}
return returnValue;},handleClick:function(e){var self=this;var $link=$(e.currentTarget);var $parent=$link.parent();var newLoc='#'+self.getHash($link);if(!$parent.hasClass(self.config.currentClass)){if(self.config.begin){self.config.begin();}
self.adjustNav(self,$parent);self.unbindInterval();self.scrollTo(newLoc,function(){if(self.config.changeHash){window.location.hash=newLoc;}
self.bindInterval();if(self.config.end){self.config.end();}});}
e.preventDefault();},scrollChange:function(){var windowTop=this.$win.scrollTop();var position=this.getSection(windowTop);var $parent;if(position!==null){$parent=this.$elem.find('a[href$="#'+position+'"]').parent();if(!$parent.hasClass(this.config.currentClass)){this.adjustNav(this,$parent);if(this.config.scrollChange){this.config.scrollChange($parent);}}}},scrollTo:function(target,callback){var offset=$(target).offset().top;$('html, body').animate({scrollTop:offset},this.config.scrollSpeed,this.config.easing,callback);},unbindInterval:function(){clearInterval(this.t);this.$win.unbind('scroll.onePageNav');}};OnePageNav.defaults=OnePageNav.prototype.defaults;$.fn.onePageNav=function(options){return this.each(function(){new OnePageNav(this,options).init();});};})(jQuery,window,document);

/*
* jquery-match-height 0.7.0 by @liabru
* http://brm.io/jquery-match-height/
* License MIT
*/
!function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):"undefined"!=typeof module&&module.exports?module.exports=t(require("jquery")):t(jQuery)}(function(t){var e=-1,o=-1,i=function(t){return parseFloat(t)||0},a=function(e){var o=1,a=t(e),n=null,r=[];return a.each(function(){var e=t(this),a=e.offset().top-i(e.css("margin-top")),s=r.length>0?r[r.length-1]:null;null===s?r.push(e):Math.floor(Math.abs(n-a))<=o?r[r.length-1]=s.add(e):r.push(e),n=a}),r},n=function(e){var o={
byRow:!0,property:"height",target:null,remove:!1};return"object"==typeof e?t.extend(o,e):("boolean"==typeof e?o.byRow=e:"remove"===e&&(o.remove=!0),o)},r=t.fn.matchHeight=function(e){var o=n(e);if(o.remove){var i=this;return this.css(o.property,""),t.each(r._groups,function(t,e){e.elements=e.elements.not(i)}),this}return this.length<=1&&!o.target?this:(r._groups.push({elements:this,options:o}),r._apply(this,o),this)};r.version="0.7.0",r._groups=[],r._throttle=80,r._maintainScroll=!1,r._beforeUpdate=null,
r._afterUpdate=null,r._rows=a,r._parse=i,r._parseOptions=n,r._apply=function(e,o){var s=n(o),h=t(e),l=[h],c=t(window).scrollTop(),p=t("html").outerHeight(!0),d=h.parents().filter(":hidden");return d.each(function(){var e=t(this);e.data("style-cache",e.attr("style"))}),d.css("display","block"),s.byRow&&!s.target&&(h.each(function(){var e=t(this),o=e.css("display");"inline-block"!==o&&"flex"!==o&&"inline-flex"!==o&&(o="block"),e.data("style-cache",e.attr("style")),e.css({display:o,"padding-top":"0",
"padding-bottom":"0","margin-top":"0","margin-bottom":"0","border-top-width":"0","border-bottom-width":"0",height:"100px",overflow:"hidden"})}),l=a(h),h.each(function(){var e=t(this);e.attr("style",e.data("style-cache")||"")})),t.each(l,function(e,o){var a=t(o),n=0;if(s.target)n=s.target.outerHeight(!1);else{if(s.byRow&&a.length<=1)return void a.css(s.property,"");a.each(function(){var e=t(this),o=e.attr("style"),i=e.css("display");"inline-block"!==i&&"flex"!==i&&"inline-flex"!==i&&(i="block");var a={
display:i};a[s.property]="",e.css(a),e.outerHeight(!1)>n&&(n=e.outerHeight(!1)),o?e.attr("style",o):e.css("display","")})}a.each(function(){var e=t(this),o=0;s.target&&e.is(s.target)||("border-box"!==e.css("box-sizing")&&(o+=i(e.css("border-top-width"))+i(e.css("border-bottom-width")),o+=i(e.css("padding-top"))+i(e.css("padding-bottom"))),e.css(s.property,n-o+"px"))})}),d.each(function(){var e=t(this);e.attr("style",e.data("style-cache")||null)}),r._maintainScroll&&t(window).scrollTop(c/p*t("html").outerHeight(!0)),
this},r._applyDataApi=function(){var e={};t("[data-match-height], [data-mh]").each(function(){var o=t(this),i=o.attr("data-mh")||o.attr("data-match-height");i in e?e[i]=e[i].add(o):e[i]=o}),t.each(e,function(){this.matchHeight(!0)})};var s=function(e){r._beforeUpdate&&r._beforeUpdate(e,r._groups),t.each(r._groups,function(){r._apply(this.elements,this.options)}),r._afterUpdate&&r._afterUpdate(e,r._groups)};r._update=function(i,a){if(a&&"resize"===a.type){var n=t(window).width();if(n===e)return;e=n;
}i?-1===o&&(o=setTimeout(function(){s(a),o=-1},r._throttle)):s(a)},t(r._applyDataApi),t(window).bind("load",function(t){r._update(!1,t)}),t(window).bind("resize orientationchange",function(t){r._update(!0,t)})});

/*! Owl carousel by Bartosz Wojciechowski/David Deutsch | v2.0.0 - http://owlcarousel2.github.io/OwlCarousel2 */
!function(a,b,c,d){function e(b,c){this.settings=null,this.options=a.extend({},e.Defaults,c),this.$element=a(b),this.drag=a.extend({},m),this.state=a.extend({},n),this.e=a.extend({},o),this._plugins={},this._supress={},this._current=null,this._speed=null,this._coordinates=[],this._breakpoint=null,this._width=null,this._items=[],this._clones=[],this._mergers=[],this._invalidated={},this._pipe=[],a.each(e.Plugins,a.proxy(function(a,b){this._plugins[a[0].toLowerCase()+a.slice(1)]=new b(this)},this)),a.each(e.Pipe,a.proxy(function(b,c){this._pipe.push({filter:c.filter,run:a.proxy(c.run,this)})},this)),this.setup(),this.initialize()}function f(a){if(a.touches!==d)return{x:a.touches[0].pageX,y:a.touches[0].pageY};if(a.touches===d){if(a.pageX!==d)return{x:a.pageX,y:a.pageY};if(a.pageX===d)return{x:a.clientX,y:a.clientY}}}function g(a){var b,d,e=c.createElement("div"),f=a;for(b in f)if(d=f[b],"undefined"!=typeof e.style[d])return e=null,[d,b];return[!1]}function h(){return g(["transition","WebkitTransition","MozTransition","OTransition"])[1]}function i(){return g(["transform","WebkitTransform","MozTransform","OTransform","msTransform"])[0]}function j(){return g(["perspective","webkitPerspective","MozPerspective","OPerspective","MsPerspective"])[0]}function k(){return"ontouchstart"in b||!!navigator.msMaxTouchPoints}function l(){return b.navigator.msPointerEnabled}var m,n,o;m={start:0,startX:0,startY:0,current:0,currentX:0,currentY:0,offsetX:0,offsetY:0,distance:null,startTime:0,endTime:0,updatedX:0,targetEl:null},n={isTouch:!1,isScrolling:!1,isSwiping:!1,direction:!1,inMotion:!1},o={_onDragStart:null,_onDragMove:null,_onDragEnd:null,_transitionEnd:null,_resizer:null,_responsiveCall:null,_goToLoop:null,_checkVisibile:null},e.Defaults={items:3,loop:!1,center:!1,mouseDrag:!0,touchDrag:!0,pullDrag:!0,freeDrag:!1,margin:0,stagePadding:0,merge:!1,mergeFit:!0,autoWidth:!1,startPosition:0,rtl:!1,smartSpeed:250,fluidSpeed:!1,dragEndSpeed:!1,responsive:{},responsiveRefreshRate:200,responsiveBaseElement:b,responsiveClass:!1,fallbackEasing:"swing",info:!1,nestedItemSelector:!1,itemElement:"div",stageElement:"div",themeClass:"owl-theme",baseClass:"owl-carousel",itemClass:"owl-item",centerClass:"center",activeClass:"active"},e.Width={Default:"default",Inner:"inner",Outer:"outer"},e.Plugins={},e.Pipe=[{filter:["width","items","settings"],run:function(a){a.current=this._items&&this._items[this.relative(this._current)]}},{filter:["items","settings"],run:function(){var a=this._clones,b=this.$stage.children(".cloned");(b.length!==a.length||!this.settings.loop&&a.length>0)&&(this.$stage.children(".cloned").remove(),this._clones=[])}},{filter:["items","settings"],run:function(){var a,b,c=this._clones,d=this._items,e=this.settings.loop?c.length-Math.max(2*this.settings.items,4):0;for(a=0,b=Math.abs(e/2);b>a;a++)e>0?(this.$stage.children().eq(d.length+c.length-1).remove(),c.pop(),this.$stage.children().eq(0).remove(),c.pop()):(c.push(c.length/2),this.$stage.append(d[c[c.length-1]].clone().addClass("cloned")),c.push(d.length-1-(c.length-1)/2),this.$stage.prepend(d[c[c.length-1]].clone().addClass("cloned")))}},{filter:["width","items","settings"],run:function(){var a,b,c,d=this.settings.rtl?1:-1,e=(this.width()/this.settings.items).toFixed(3),f=0;for(this._coordinates=[],b=0,c=this._clones.length+this._items.length;c>b;b++)a=this._mergers[this.relative(b)],a=this.settings.mergeFit&&Math.min(a,this.settings.items)||a,f+=(this.settings.autoWidth?this._items[this.relative(b)].width()+this.settings.margin:e*a)*d,this._coordinates.push(f)}},{filter:["width","items","settings"],run:function(){var b,c,d=(this.width()/this.settings.items).toFixed(3),e={width:Math.abs(this._coordinates[this._coordinates.length-1])+2*this.settings.stagePadding,"padding-left":this.settings.stagePadding||"","padding-right":this.settings.stagePadding||""};if(this.$stage.css(e),e={width:this.settings.autoWidth?"auto":d-this.settings.margin},e[this.settings.rtl?"margin-left":"margin-right"]=this.settings.margin,!this.settings.autoWidth&&a.grep(this._mergers,function(a){return a>1}).length>0)for(b=0,c=this._coordinates.length;c>b;b++)e.width=Math.abs(this._coordinates[b])-Math.abs(this._coordinates[b-1]||0)-this.settings.margin,this.$stage.children().eq(b).css(e);else this.$stage.children().css(e)}},{filter:["width","items","settings"],run:function(a){a.current&&this.reset(this.$stage.children().index(a.current))}},{filter:["position"],run:function(){this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:function(){var a,b,c,d,e=this.settings.rtl?1:-1,f=2*this.settings.stagePadding,g=this.coordinates(this.current())+f,h=g+this.width()*e,i=[];for(c=0,d=this._coordinates.length;d>c;c++)a=this._coordinates[c-1]||0,b=Math.abs(this._coordinates[c])+f*e,(this.op(a,"<=",g)&&this.op(a,">",h)||this.op(b,"<",g)&&this.op(b,">",h))&&i.push(c);this.$stage.children("."+this.settings.activeClass).removeClass(this.settings.activeClass),this.$stage.children(":eq("+i.join("), :eq(")+")").addClass(this.settings.activeClass),this.settings.center&&(this.$stage.children("."+this.settings.centerClass).removeClass(this.settings.centerClass),this.$stage.children().eq(this.current()).addClass(this.settings.centerClass))}}],e.prototype.initialize=function(){if(this.trigger("initialize"),this.$element.addClass(this.settings.baseClass).addClass(this.settings.themeClass).toggleClass("owl-rtl",this.settings.rtl),this.browserSupport(),this.settings.autoWidth&&this.state.imagesLoaded!==!0){var b,c,e;if(b=this.$element.find("img"),c=this.settings.nestedItemSelector?"."+this.settings.nestedItemSelector:d,e=this.$element.children(c).width(),b.length&&0>=e)return this.preloadAutoWidthImages(b),!1}this.$element.addClass("owl-loading"),this.$stage=a("<"+this.settings.stageElement+' class="owl-stage"/>').wrap('<div class="owl-stage-outer">'),this.$element.append(this.$stage.parent()),this.replace(this.$element.children().not(this.$stage.parent())),this._width=this.$element.width(),this.refresh(),this.$element.removeClass("owl-loading").addClass("owl-loaded"),this.eventsCall(),this.internalEvents(),this.addTriggerableEvents(),this.trigger("initialized")},e.prototype.setup=function(){var b=this.viewport(),c=this.options.responsive,d=-1,e=null;c?(a.each(c,function(a){b>=a&&a>d&&(d=Number(a))}),e=a.extend({},this.options,c[d]),delete e.responsive,e.responsiveClass&&this.$element.attr("class",function(a,b){return b.replace(/\b owl-responsive-\S+/g,"")}).addClass("owl-responsive-"+d)):e=a.extend({},this.options),(null===this.settings||this._breakpoint!==d)&&(this.trigger("change",{property:{name:"settings",value:e}}),this._breakpoint=d,this.settings=e,this.invalidate("settings"),this.trigger("changed",{property:{name:"settings",value:this.settings}}))},e.prototype.optionsLogic=function(){this.$element.toggleClass("owl-center",this.settings.center),this.settings.loop&&this._items.length<this.settings.items&&(this.settings.loop=!1),this.settings.autoWidth&&(this.settings.stagePadding=!1,this.settings.merge=!1)},e.prototype.prepare=function(b){var c=this.trigger("prepare",{content:b});return c.data||(c.data=a("<"+this.settings.itemElement+"/>").addClass(this.settings.itemClass).append(b)),this.trigger("prepared",{content:c.data}),c.data},e.prototype.update=function(){for(var b=0,c=this._pipe.length,d=a.proxy(function(a){return this[a]},this._invalidated),e={};c>b;)(this._invalidated.all||a.grep(this._pipe[b].filter,d).length>0)&&this._pipe[b].run(e),b++;this._invalidated={}},e.prototype.width=function(a){switch(a=a||e.Width.Default){case e.Width.Inner:case e.Width.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}},e.prototype.refresh=function(){if(0===this._items.length)return!1;(new Date).getTime();this.trigger("refresh"),this.setup(),this.optionsLogic(),this.$stage.addClass("owl-refresh"),this.update(),this.$stage.removeClass("owl-refresh"),this.state.orientation=b.orientation,this.watchVisibility(),this.trigger("refreshed")},e.prototype.eventsCall=function(){this.e._onDragStart=a.proxy(function(a){this.onDragStart(a)},this),this.e._onDragMove=a.proxy(function(a){this.onDragMove(a)},this),this.e._onDragEnd=a.proxy(function(a){this.onDragEnd(a)},this),this.e._onResize=a.proxy(function(a){this.onResize(a)},this),this.e._transitionEnd=a.proxy(function(a){this.transitionEnd(a)},this),this.e._preventClick=a.proxy(function(a){this.preventClick(a)},this)},e.prototype.onThrottledResize=function(){b.clearTimeout(this.resizeTimer),this.resizeTimer=b.setTimeout(this.e._onResize,this.settings.responsiveRefreshRate)},e.prototype.onResize=function(){return this._items.length?this._width===this.$element.width()?!1:this.trigger("resize").isDefaultPrevented()?!1:(this._width=this.$element.width(),this.invalidate("width"),this.refresh(),void this.trigger("resized")):!1},e.prototype.eventsRouter=function(a){var b=a.type;"mousedown"===b||"touchstart"===b?this.onDragStart(a):"mousemove"===b||"touchmove"===b?this.onDragMove(a):"mouseup"===b||"touchend"===b?this.onDragEnd(a):"touchcancel"===b&&this.onDragEnd(a)},e.prototype.internalEvents=function(){var c=(k(),l());this.settings.mouseDrag?(this.$stage.on("mousedown",a.proxy(function(a){this.eventsRouter(a)},this)),this.$stage.on("dragstart",function(){return!1}),this.$stage.get(0).onselectstart=function(){return!1}):this.$element.addClass("owl-text-select-on"),this.settings.touchDrag&&!c&&this.$stage.on("touchstart touchcancel",a.proxy(function(a){this.eventsRouter(a)},this)),this.transitionEndVendor&&this.on(this.$stage.get(0),this.transitionEndVendor,this.e._transitionEnd,!1),this.settings.responsive!==!1&&this.on(b,"resize",a.proxy(this.onThrottledResize,this))},e.prototype.onDragStart=function(d){var e,g,h,i;if(e=d.originalEvent||d||b.event,3===e.which||this.state.isTouch)return!1;if("mousedown"===e.type&&this.$stage.addClass("owl-grab"),this.trigger("drag"),this.drag.startTime=(new Date).getTime(),this.speed(0),this.state.isTouch=!0,this.state.isScrolling=!1,this.state.isSwiping=!1,this.drag.distance=0,g=f(e).x,h=f(e).y,this.drag.offsetX=this.$stage.position().left,this.drag.offsetY=this.$stage.position().top,this.settings.rtl&&(this.drag.offsetX=this.$stage.position().left+this.$stage.width()-this.width()+this.settings.margin),this.state.inMotion&&this.support3d)i=this.getTransformProperty(),this.drag.offsetX=i,this.animate(i),this.state.inMotion=!0;else if(this.state.inMotion&&!this.support3d)return this.state.inMotion=!1,!1;this.drag.startX=g-this.drag.offsetX,this.drag.startY=h-this.drag.offsetY,this.drag.start=g-this.drag.startX,this.drag.targetEl=e.target||e.srcElement,this.drag.updatedX=this.drag.start,("IMG"===this.drag.targetEl.tagName||"A"===this.drag.targetEl.tagName)&&(this.drag.targetEl.draggable=!1),a(c).on("mousemove.owl.dragEvents mouseup.owl.dragEvents touchmove.owl.dragEvents touchend.owl.dragEvents",a.proxy(function(a){this.eventsRouter(a)},this))},e.prototype.onDragMove=function(a){var c,e,g,h,i,j;this.state.isTouch&&(this.state.isScrolling||(c=a.originalEvent||a||b.event,e=f(c).x,g=f(c).y,this.drag.currentX=e-this.drag.startX,this.drag.currentY=g-this.drag.startY,this.drag.distance=this.drag.currentX-this.drag.offsetX,this.drag.distance<0?this.state.direction=this.settings.rtl?"right":"left":this.drag.distance>0&&(this.state.direction=this.settings.rtl?"left":"right"),this.settings.loop?this.op(this.drag.currentX,">",this.coordinates(this.minimum()))&&"right"===this.state.direction?this.drag.currentX-=(this.settings.center&&this.coordinates(0))-this.coordinates(this._items.length):this.op(this.drag.currentX,"<",this.coordinates(this.maximum()))&&"left"===this.state.direction&&(this.drag.currentX+=(this.settings.center&&this.coordinates(0))-this.coordinates(this._items.length)):(h=this.coordinates(this.settings.rtl?this.maximum():this.minimum()),i=this.coordinates(this.settings.rtl?this.minimum():this.maximum()),j=this.settings.pullDrag?this.drag.distance/5:0,this.drag.currentX=Math.max(Math.min(this.drag.currentX,h+j),i+j)),(this.drag.distance>8||this.drag.distance<-8)&&(c.preventDefault!==d?c.preventDefault():c.returnValue=!1,this.state.isSwiping=!0),this.drag.updatedX=this.drag.currentX,(this.drag.currentY>16||this.drag.currentY<-16)&&this.state.isSwiping===!1&&(this.state.isScrolling=!0,this.drag.updatedX=this.drag.start),this.animate(this.drag.updatedX)))},e.prototype.onDragEnd=function(b){var d,e,f;if(this.state.isTouch){if("mouseup"===b.type&&this.$stage.removeClass("owl-grab"),this.trigger("dragged"),this.drag.targetEl.removeAttribute("draggable"),this.state.isTouch=!1,this.state.isScrolling=!1,this.state.isSwiping=!1,0===this.drag.distance&&this.state.inMotion!==!0)return this.state.inMotion=!1,!1;this.drag.endTime=(new Date).getTime(),d=this.drag.endTime-this.drag.startTime,e=Math.abs(this.drag.distance),(e>3||d>300)&&this.removeClick(this.drag.targetEl),f=this.closest(this.drag.updatedX),this.speed(this.settings.dragEndSpeed||this.settings.smartSpeed),this.current(f),this.invalidate("position"),this.update(),this.settings.pullDrag||this.drag.updatedX!==this.coordinates(f)||this.transitionEnd(),this.drag.distance=0,a(c).off(".owl.dragEvents")}},e.prototype.removeClick=function(c){this.drag.targetEl=c,a(c).on("click.preventClick",this.e._preventClick),b.setTimeout(function(){a(c).off("click.preventClick")},300)},e.prototype.preventClick=function(b){b.preventDefault?b.preventDefault():b.returnValue=!1,b.stopPropagation&&b.stopPropagation(),a(b.target).off("click.preventClick")},e.prototype.getTransformProperty=function(){var a,c;return a=b.getComputedStyle(this.$stage.get(0),null).getPropertyValue(this.vendorName+"transform"),a=a.replace(/matrix(3d)?\(|\)/g,"").split(","),c=16===a.length,c!==!0?a[4]:a[12]},e.prototype.closest=function(b){var c=-1,d=30,e=this.width(),f=this.coordinates();return this.settings.freeDrag||a.each(f,a.proxy(function(a,g){return b>g-d&&g+d>b?c=a:this.op(b,"<",g)&&this.op(b,">",f[a+1]||g-e)&&(c="left"===this.state.direction?a+1:a),-1===c},this)),this.settings.loop||(this.op(b,">",f[this.minimum()])?c=b=this.minimum():this.op(b,"<",f[this.maximum()])&&(c=b=this.maximum())),c},e.prototype.animate=function(b){this.trigger("translate"),this.state.inMotion=this.speed()>0,this.support3d?this.$stage.css({transform:"translate3d("+b+"px,0px, 0px)",transition:this.speed()/1e3+"s"}):this.state.isTouch?this.$stage.css({left:b+"px"}):this.$stage.animate({left:b},this.speed()/1e3,this.settings.fallbackEasing,a.proxy(function(){this.state.inMotion&&this.transitionEnd()},this))},e.prototype.current=function(a){if(a===d)return this._current;if(0===this._items.length)return d;if(a=this.normalize(a),this._current!==a){var b=this.trigger("change",{property:{name:"position",value:a}});b.data!==d&&(a=this.normalize(b.data)),this._current=a,this.invalidate("position"),this.trigger("changed",{property:{name:"position",value:this._current}})}return this._current},e.prototype.invalidate=function(a){this._invalidated[a]=!0},e.prototype.reset=function(a){a=this.normalize(a),a!==d&&(this._speed=0,this._current=a,this.suppress(["translate","translated"]),this.animate(this.coordinates(a)),this.release(["translate","translated"]))},e.prototype.normalize=function(b,c){var e=c?this._items.length:this._items.length+this._clones.length;return!a.isNumeric(b)||1>e?d:b=this._clones.length?(b%e+e)%e:Math.max(this.minimum(c),Math.min(this.maximum(c),b))},e.prototype.relative=function(a){return a=this.normalize(a),a-=this._clones.length/2,this.normalize(a,!0)},e.prototype.maximum=function(a){var b,c,d,e=0,f=this.settings;if(a)return this._items.length-1;if(!f.loop&&f.center)b=this._items.length-1;else if(f.loop||f.center)if(f.loop||f.center)b=this._items.length+f.items;else{if(!f.autoWidth&&!f.merge)throw"Can not detect maximum absolute position.";for(revert=f.rtl?1:-1,c=this.$stage.width()-this.$element.width();(d=this.coordinates(e))&&!(d*revert>=c);)b=++e}else b=this._items.length-f.items;return b},e.prototype.minimum=function(a){return a?0:this._clones.length/2},e.prototype.items=function(a){return a===d?this._items.slice():(a=this.normalize(a,!0),this._items[a])},e.prototype.mergers=function(a){return a===d?this._mergers.slice():(a=this.normalize(a,!0),this._mergers[a])},e.prototype.clones=function(b){var c=this._clones.length/2,e=c+this._items.length,f=function(a){return a%2===0?e+a/2:c-(a+1)/2};return b===d?a.map(this._clones,function(a,b){return f(b)}):a.map(this._clones,function(a,c){return a===b?f(c):null})},e.prototype.speed=function(a){return a!==d&&(this._speed=a),this._speed},e.prototype.coordinates=function(b){var c=null;return b===d?a.map(this._coordinates,a.proxy(function(a,b){return this.coordinates(b)},this)):(this.settings.center?(c=this._coordinates[b],c+=(this.width()-c+(this._coordinates[b-1]||0))/2*(this.settings.rtl?-1:1)):c=this._coordinates[b-1]||0,c)},e.prototype.duration=function(a,b,c){return Math.min(Math.max(Math.abs(b-a),1),6)*Math.abs(c||this.settings.smartSpeed)},e.prototype.to=function(c,d){if(this.settings.loop){var e=c-this.relative(this.current()),f=this.current(),g=this.current(),h=this.current()+e,i=0>g-h?!0:!1,j=this._clones.length+this._items.length;h<this.settings.items&&i===!1?(f=g+this._items.length,this.reset(f)):h>=j-this.settings.items&&i===!0&&(f=g-this._items.length,this.reset(f)),b.clearTimeout(this.e._goToLoop),this.e._goToLoop=b.setTimeout(a.proxy(function(){this.speed(this.duration(this.current(),f+e,d)),this.current(f+e),this.update()},this),30)}else this.speed(this.duration(this.current(),c,d)),this.current(c),this.update()},e.prototype.next=function(a){a=a||!1,this.to(this.relative(this.current())+1,a)},e.prototype.prev=function(a){a=a||!1,this.to(this.relative(this.current())-1,a)},e.prototype.transitionEnd=function(a){return a!==d&&(a.stopPropagation(),(a.target||a.srcElement||a.originalTarget)!==this.$stage.get(0))?!1:(this.state.inMotion=!1,void this.trigger("translated"))},e.prototype.viewport=function(){var d;if(this.options.responsiveBaseElement!==b)d=a(this.options.responsiveBaseElement).width();else if(b.innerWidth)d=b.innerWidth;else{if(!c.documentElement||!c.documentElement.clientWidth)throw"Can not detect viewport width.";d=c.documentElement.clientWidth}return d},e.prototype.replace=function(b){this.$stage.empty(),this._items=[],b&&(b=b instanceof jQuery?b:a(b)),this.settings.nestedItemSelector&&(b=b.find("."+this.settings.nestedItemSelector)),b.filter(function(){return 1===this.nodeType}).each(a.proxy(function(a,b){b=this.prepare(b),this.$stage.append(b),this._items.push(b),this._mergers.push(1*b.find("[data-merge]").andSelf("[data-merge]").attr("data-merge")||1)},this)),this.reset(a.isNumeric(this.settings.startPosition)?this.settings.startPosition:0),this.invalidate("items")},e.prototype.add=function(a,b){b=b===d?this._items.length:this.normalize(b,!0),this.trigger("add",{content:a,position:b}),0===this._items.length||b===this._items.length?(this.$stage.append(a),this._items.push(a),this._mergers.push(1*a.find("[data-merge]").andSelf("[data-merge]").attr("data-merge")||1)):(this._items[b].before(a),this._items.splice(b,0,a),this._mergers.splice(b,0,1*a.find("[data-merge]").andSelf("[data-merge]").attr("data-merge")||1)),this.invalidate("items"),this.trigger("added",{content:a,position:b})},e.prototype.remove=function(a){a=this.normalize(a,!0),a!==d&&(this.trigger("remove",{content:this._items[a],position:a}),this._items[a].remove(),this._items.splice(a,1),this._mergers.splice(a,1),this.invalidate("items"),this.trigger("removed",{content:null,position:a}))},e.prototype.addTriggerableEvents=function(){var b=a.proxy(function(b,c){return a.proxy(function(a){a.relatedTarget!==this&&(this.suppress([c]),b.apply(this,[].slice.call(arguments,1)),this.release([c]))},this)},this);a.each({next:this.next,prev:this.prev,to:this.to,destroy:this.destroy,refresh:this.refresh,replace:this.replace,add:this.add,remove:this.remove},a.proxy(function(a,c){this.$element.on(a+".owl.carousel",b(c,a+".owl.carousel"))},this))},e.prototype.watchVisibility=function(){function c(a){return a.offsetWidth>0&&a.offsetHeight>0}function d(){c(this.$element.get(0))&&(this.$element.removeClass("owl-hidden"),this.refresh(),b.clearInterval(this.e._checkVisibile))}c(this.$element.get(0))||(this.$element.addClass("owl-hidden"),b.clearInterval(this.e._checkVisibile),this.e._checkVisibile=b.setInterval(a.proxy(d,this),500))},e.prototype.preloadAutoWidthImages=function(b){var c,d,e,f;c=0,d=this,b.each(function(g,h){e=a(h),f=new Image,f.onload=function(){c++,e.attr("src",f.src),e.css("opacity",1),c>=b.length&&(d.state.imagesLoaded=!0,d.initialize())},f.src=e.attr("src")||e.attr("data-src")||e.attr("data-src-retina")})},e.prototype.destroy=function(){this.$element.hasClass(this.settings.themeClass)&&this.$element.removeClass(this.settings.themeClass),this.settings.responsive!==!1&&a(b).off("resize.owl.carousel"),this.transitionEndVendor&&this.off(this.$stage.get(0),this.transitionEndVendor,this.e._transitionEnd);for(var d in this._plugins)this._plugins[d].destroy();(this.settings.mouseDrag||this.settings.touchDrag)&&(this.$stage.off("mousedown touchstart touchcancel"),a(c).off(".owl.dragEvents"),this.$stage.get(0).onselectstart=function(){},this.$stage.off("dragstart",function(){return!1})),this.$element.off(".owl"),this.$stage.children(".cloned").remove(),this.e=null,this.$element.removeData("owlCarousel"),this.$stage.children().contents().unwrap(),this.$stage.children().unwrap(),this.$stage.unwrap()},e.prototype.op=function(a,b,c){var d=this.settings.rtl;switch(b){case"<":return d?a>c:c>a;case">":return d?c>a:a>c;case">=":return d?c>=a:a>=c;case"<=":return d?a>=c:c>=a}},e.prototype.on=function(a,b,c,d){a.addEventListener?a.addEventListener(b,c,d):a.attachEvent&&a.attachEvent("on"+b,c)},e.prototype.off=function(a,b,c,d){a.removeEventListener?a.removeEventListener(b,c,d):a.detachEvent&&a.detachEvent("on"+b,c)},e.prototype.trigger=function(b,c,d){var e={item:{count:this._items.length,index:this.current()}},f=a.camelCase(a.grep(["on",b,d],function(a){return a}).join("-").toLowerCase()),g=a.Event([b,"owl",d||"carousel"].join(".").toLowerCase(),a.extend({relatedTarget:this},e,c));return this._supress[b]||(a.each(this._plugins,function(a,b){b.onTrigger&&b.onTrigger(g)}),this.$element.trigger(g),this.settings&&"function"==typeof this.settings[f]&&this.settings[f].apply(this,g)),g},e.prototype.suppress=function(b){a.each(b,a.proxy(function(a,b){this._supress[b]=!0},this))},e.prototype.release=function(b){a.each(b,a.proxy(function(a,b){delete this._supress[b]},this))},e.prototype.browserSupport=function(){if(this.support3d=j(),this.support3d){this.transformVendor=i();var a=["transitionend","webkitTransitionEnd","transitionend","oTransitionEnd"];this.transitionEndVendor=a[h()],this.vendorName=this.transformVendor.replace(/Transform/i,""),this.vendorName=""!==this.vendorName?"-"+this.vendorName.toLowerCase()+"-":""}this.state.orientation=b.orientation},a.fn.owlCarousel=function(b){return this.each(function(){a(this).data("owlCarousel")||a(this).data("owlCarousel",new e(this,b))})},a.fn.owlCarousel.Constructor=e}(window.Zepto||window.jQuery,window,document),function(a,b){var c=function(b){this._core=b,this._loaded=[],this._handlers={"initialized.owl.carousel change.owl.carousel":a.proxy(function(b){if(b.namespace&&this._core.settings&&this._core.settings.lazyLoad&&(b.property&&"position"==b.property.name||"initialized"==b.type))for(var c=this._core.settings,d=c.center&&Math.ceil(c.items/2)||c.items,e=c.center&&-1*d||0,f=(b.property&&b.property.value||this._core.current())+e,g=this._core.clones().length,h=a.proxy(function(a,b){this.load(b)},this);e++<d;)this.load(g/2+this._core.relative(f)),g&&a.each(this._core.clones(this._core.relative(f++)),h)},this)},this._core.options=a.extend({},c.Defaults,this._core.options),this._core.$element.on(this._handlers)};c.Defaults={lazyLoad:!1},c.prototype.load=function(c){var d=this._core.$stage.children().eq(c),e=d&&d.find(".owl-lazy");!e||a.inArray(d.get(0),this._loaded)>-1||(e.each(a.proxy(function(c,d){var e,f=a(d),g=b.devicePixelRatio>1&&f.attr("data-src-retina")||f.attr("data-src");this._core.trigger("load",{element:f,url:g},"lazy"),f.is("img")?f.one("load.owl.lazy",a.proxy(function(){f.css("opacity",1),this._core.trigger("loaded",{element:f,url:g},"lazy")},this)).attr("src",g):(e=new Image,e.onload=a.proxy(function(){f.css({"background-image":"url("+g+")",opacity:"1"}),this._core.trigger("loaded",{element:f,url:g},"lazy")},this),e.src=g)},this)),this._loaded.push(d.get(0)))},c.prototype.destroy=function(){var a,b;for(a in this.handlers)this._core.$element.off(a,this.handlers[a]);for(b in Object.getOwnPropertyNames(this))"function"!=typeof this[b]&&(this[b]=null)},a.fn.owlCarousel.Constructor.Plugins.Lazy=c}(window.Zepto||window.jQuery,window,document),function(a){var b=function(c){this._core=c,this._handlers={"initialized.owl.carousel":a.proxy(function(){this._core.settings.autoHeight&&this.update()},this),"changed.owl.carousel":a.proxy(function(a){this._core.settings.autoHeight&&"position"==a.property.name&&this.update()},this),"loaded.owl.lazy":a.proxy(function(a){this._core.settings.autoHeight&&a.element.closest("."+this._core.settings.itemClass)===this._core.$stage.children().eq(this._core.current())&&this.update()},this)},this._core.options=a.extend({},b.Defaults,this._core.options),this._core.$element.on(this._handlers)};b.Defaults={autoHeight:!1,autoHeightClass:"owl-height"},b.prototype.update=function(){this._core.$stage.parent().height(this._core.$stage.children().eq(this._core.current()).height()).addClass(this._core.settings.autoHeightClass)},b.prototype.destroy=function(){var a,b;for(a in this._handlers)this._core.$element.off(a,this._handlers[a]);for(b in Object.getOwnPropertyNames(this))"function"!=typeof this[b]&&(this[b]=null)},a.fn.owlCarousel.Constructor.Plugins.AutoHeight=b}(window.Zepto||window.jQuery,window,document),function(a,b,c){var d=function(b){this._core=b,this._videos={},this._playing=null,this._fullscreen=!1,this._handlers={"resize.owl.carousel":a.proxy(function(a){this._core.settings.video&&!this.isInFullScreen()&&a.preventDefault()},this),"refresh.owl.carousel changed.owl.carousel":a.proxy(function(){this._playing&&this.stop()},this),"prepared.owl.carousel":a.proxy(function(b){var c=a(b.content).find(".owl-video");c.length&&(c.css("display","none"),this.fetch(c,a(b.content)))},this)},this._core.options=a.extend({},d.Defaults,this._core.options),this._core.$element.on(this._handlers),this._core.$element.on("click.owl.video",".owl-video-play-icon",a.proxy(function(a){this.play(a)},this))};d.Defaults={video:!1,videoHeight:!1,videoWidth:!1},d.prototype.fetch=function(a,b){var c=a.attr("data-vimeo-id")?"vimeo":"youtube",d=a.attr("data-vimeo-id")||a.attr("data-youtube-id"),e=a.attr("data-width")||this._core.settings.videoWidth,f=a.attr("data-height")||this._core.settings.videoHeight,g=a.attr("href");if(!g)throw new Error("Missing video URL.");if(d=g.match(/(http:|https:|)\/\/(player.|www.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com))\/(video\/|embed\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/),d[3].indexOf("youtu")>-1)c="youtube";else{if(!(d[3].indexOf("vimeo")>-1))throw new Error("Video URL not supported.");c="vimeo"}d=d[6],this._videos[g]={type:c,id:d,width:e,height:f},b.attr("data-video",g),this.thumbnail(a,this._videos[g])},d.prototype.thumbnail=function(b,c){var d,e,f,g=c.width&&c.height?'style="width:'+c.width+"px;height:"+c.height+'px;"':"",h=b.find("img"),i="src",j="",k=this._core.settings,l=function(a){e='<div class="owl-video-play-icon"></div>',d=k.lazyLoad?'<div class="owl-video-tn '+j+'" '+i+'="'+a+'"></div>':'<div class="owl-video-tn" style="opacity:1;background-image:url('+a+')"></div>',b.after(d),b.after(e)};return b.wrap('<div class="owl-video-wrapper"'+g+"></div>"),this._core.settings.lazyLoad&&(i="data-src",j="owl-lazy"),h.length?(l(h.attr(i)),h.remove(),!1):void("youtube"===c.type?(f="http://img.youtube.com/vi/"+c.id+"/hqdefault.jpg",l(f)):"vimeo"===c.type&&a.ajax({type:"GET",url:"http://vimeo.com/api/v2/video/"+c.id+".json",jsonp:"callback",dataType:"jsonp",success:function(a){f=a[0].thumbnail_large,l(f)}}))},d.prototype.stop=function(){this._core.trigger("stop",null,"video"),this._playing.find(".owl-video-frame").remove(),this._playing.removeClass("owl-video-playing"),this._playing=null},d.prototype.play=function(b){this._core.trigger("play",null,"video"),this._playing&&this.stop();var c,d,e=a(b.target||b.srcElement),f=e.closest("."+this._core.settings.itemClass),g=this._videos[f.attr("data-video")],h=g.width||"100%",i=g.height||this._core.$stage.height();"youtube"===g.type?c='<iframe width="'+h+'" height="'+i+'" src="http://www.youtube.com/embed/'+g.id+"?autoplay=1&v="+g.id+'" frameborder="0" allowfullscreen></iframe>':"vimeo"===g.type&&(c='<iframe src="http://player.vimeo.com/video/'+g.id+'?autoplay=1" width="'+h+'" height="'+i+'" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>'),f.addClass("owl-video-playing"),this._playing=f,d=a('<div style="height:'+i+"px; width:"+h+'px" class="owl-video-frame">'+c+"</div>"),e.after(d)},d.prototype.isInFullScreen=function(){var d=c.fullscreenElement||c.mozFullScreenElement||c.webkitFullscreenElement;return d&&a(d).parent().hasClass("owl-video-frame")&&(this._core.speed(0),this._fullscreen=!0),d&&this._fullscreen&&this._playing?!1:this._fullscreen?(this._fullscreen=!1,!1):this._playing&&this._core.state.orientation!==b.orientation?(this._core.state.orientation=b.orientation,!1):!0},d.prototype.destroy=function(){var a,b;this._core.$element.off("click.owl.video");for(a in this._handlers)this._core.$element.off(a,this._handlers[a]);for(b in Object.getOwnPropertyNames(this))"function"!=typeof this[b]&&(this[b]=null)},a.fn.owlCarousel.Constructor.Plugins.Video=d}(window.Zepto||window.jQuery,window,document),function(a,b,c,d){var e=function(b){this.core=b,this.core.options=a.extend({},e.Defaults,this.core.options),this.swapping=!0,this.previous=d,this.next=d,this.handlers={"change.owl.carousel":a.proxy(function(a){"position"==a.property.name&&(this.previous=this.core.current(),this.next=a.property.value)},this),"drag.owl.carousel dragged.owl.carousel translated.owl.carousel":a.proxy(function(a){this.swapping="translated"==a.type},this),"translate.owl.carousel":a.proxy(function(){this.swapping&&(this.core.options.animateOut||this.core.options.animateIn)&&this.swap()},this)},this.core.$element.on(this.handlers)};e.Defaults={animateOut:!1,animateIn:!1},e.prototype.swap=function(){if(1===this.core.settings.items&&this.core.support3d){this.core.speed(0);var b,c=a.proxy(this.clear,this),d=this.core.$stage.children().eq(this.previous),e=this.core.$stage.children().eq(this.next),f=this.core.settings.animateIn,g=this.core.settings.animateOut;this.core.current()!==this.previous&&(g&&(b=this.core.coordinates(this.previous)-this.core.coordinates(this.next),d.css({left:b+"px"}).addClass("animated owl-animated-out").addClass(g).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",c)),f&&e.addClass("animated owl-animated-in").addClass(f).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",c))}},e.prototype.clear=function(b){a(b.target).css({left:""}).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut),this.core.transitionEnd()},e.prototype.destroy=function(){var a,b;for(a in this.handlers)this.core.$element.off(a,this.handlers[a]);for(b in Object.getOwnPropertyNames(this))"function"!=typeof this[b]&&(this[b]=null)},a.fn.owlCarousel.Constructor.Plugins.Animate=e}(window.Zepto||window.jQuery,window,document),function(a,b,c){var d=function(b){this.core=b,this.core.options=a.extend({},d.Defaults,this.core.options),this.handlers={"translated.owl.carousel refreshed.owl.carousel":a.proxy(function(){this.autoplay()
},this),"play.owl.autoplay":a.proxy(function(a,b,c){this.play(b,c)},this),"stop.owl.autoplay":a.proxy(function(){this.stop()},this),"mouseover.owl.autoplay":a.proxy(function(){this.core.settings.autoplayHoverPause&&this.pause()},this),"mouseleave.owl.autoplay":a.proxy(function(){this.core.settings.autoplayHoverPause&&this.autoplay()},this)},this.core.$element.on(this.handlers)};d.Defaults={autoplay:!1,autoplayTimeout:5e3,autoplayHoverPause:!1,autoplaySpeed:!1},d.prototype.autoplay=function(){this.core.settings.autoplay&&!this.core.state.videoPlay?(b.clearInterval(this.interval),this.interval=b.setInterval(a.proxy(function(){this.play()},this),this.core.settings.autoplayTimeout)):b.clearInterval(this.interval)},d.prototype.play=function(){return c.hidden===!0||this.core.state.isTouch||this.core.state.isScrolling||this.core.state.isSwiping||this.core.state.inMotion?void 0:this.core.settings.autoplay===!1?void b.clearInterval(this.interval):void this.core.next(this.core.settings.autoplaySpeed)},d.prototype.stop=function(){b.clearInterval(this.interval)},d.prototype.pause=function(){b.clearInterval(this.interval)},d.prototype.destroy=function(){var a,c;b.clearInterval(this.interval);for(a in this.handlers)this.core.$element.off(a,this.handlers[a]);for(c in Object.getOwnPropertyNames(this))"function"!=typeof this[c]&&(this[c]=null)},a.fn.owlCarousel.Constructor.Plugins.autoplay=d}(window.Zepto||window.jQuery,window,document),function(a){"use strict";var b=function(c){this._core=c,this._initialized=!1,this._pages=[],this._controls={},this._templates=[],this.$element=this._core.$element,this._overrides={next:this._core.next,prev:this._core.prev,to:this._core.to},this._handlers={"prepared.owl.carousel":a.proxy(function(b){this._core.settings.dotsData&&this._templates.push(a(b.content).find("[data-dot]").andSelf("[data-dot]").attr("data-dot"))},this),"add.owl.carousel":a.proxy(function(b){this._core.settings.dotsData&&this._templates.splice(b.position,0,a(b.content).find("[data-dot]").andSelf("[data-dot]").attr("data-dot"))},this),"remove.owl.carousel prepared.owl.carousel":a.proxy(function(a){this._core.settings.dotsData&&this._templates.splice(a.position,1)},this),"change.owl.carousel":a.proxy(function(a){if("position"==a.property.name&&!this._core.state.revert&&!this._core.settings.loop&&this._core.settings.navRewind){var b=this._core.current(),c=this._core.maximum(),d=this._core.minimum();a.data=a.property.value>c?b>=c?d:c:a.property.value<d?c:a.property.value}},this),"changed.owl.carousel":a.proxy(function(a){"position"==a.property.name&&this.draw()},this),"refreshed.owl.carousel":a.proxy(function(){this._initialized||(this.initialize(),this._initialized=!0),this._core.trigger("refresh",null,"navigation"),this.update(),this.draw(),this._core.trigger("refreshed",null,"navigation")},this)},this._core.options=a.extend({},b.Defaults,this._core.options),this.$element.on(this._handlers)};b.Defaults={nav:!1,navRewind:!0,navText:["prev","next"],navSpeed:!1,navElement:"div",navContainer:!1,navContainerClass:"owl-nav",navClass:["owl-prev","owl-next"],slideBy:1,dotClass:"owl-dot",dotsClass:"owl-dots",dots:!0,dotsEach:!1,dotData:!1,dotsSpeed:!1,dotsContainer:!1,controlsClass:"owl-controls"},b.prototype.initialize=function(){var b,c,d=this._core.settings;d.dotsData||(this._templates=[a("<div>").addClass(d.dotClass).append(a("<span>")).prop("outerHTML")]),d.navContainer&&d.dotsContainer||(this._controls.$container=a("<div>").addClass(d.controlsClass).appendTo(this.$element)),this._controls.$indicators=d.dotsContainer?a(d.dotsContainer):a("<div>").hide().addClass(d.dotsClass).appendTo(this._controls.$container),this._controls.$indicators.on("click","div",a.proxy(function(b){var c=a(b.target).parent().is(this._controls.$indicators)?a(b.target).index():a(b.target).parent().index();b.preventDefault(),this.to(c,d.dotsSpeed)},this)),b=d.navContainer?a(d.navContainer):a("<div>").addClass(d.navContainerClass).prependTo(this._controls.$container),this._controls.$next=a("<"+d.navElement+">"),this._controls.$previous=this._controls.$next.clone(),this._controls.$previous.addClass(d.navClass[0]).html(d.navText[0]).hide().prependTo(b).on("click",a.proxy(function(){this.prev(d.navSpeed)},this)),this._controls.$next.addClass(d.navClass[1]).html(d.navText[1]).hide().appendTo(b).on("click",a.proxy(function(){this.next(d.navSpeed)},this));for(c in this._overrides)this._core[c]=a.proxy(this[c],this)},b.prototype.destroy=function(){var a,b,c,d;for(a in this._handlers)this.$element.off(a,this._handlers[a]);for(b in this._controls)this._controls[b].remove();for(d in this.overides)this._core[d]=this._overrides[d];for(c in Object.getOwnPropertyNames(this))"function"!=typeof this[c]&&(this[c]=null)},b.prototype.update=function(){var a,b,c,d=this._core.settings,e=this._core.clones().length/2,f=e+this._core.items().length,g=d.center||d.autoWidth||d.dotData?1:d.dotsEach||d.items;if("page"!==d.slideBy&&(d.slideBy=Math.min(d.slideBy,d.items)),d.dots||"page"==d.slideBy)for(this._pages=[],a=e,b=0,c=0;f>a;a++)(b>=g||0===b)&&(this._pages.push({start:a-e,end:a-e+g-1}),b=0,++c),b+=this._core.mergers(this._core.relative(a))},b.prototype.draw=function(){var b,c,d="",e=this._core.settings,f=(this._core.$stage.children(),this._core.relative(this._core.current()));if(!e.nav||e.loop||e.navRewind||(this._controls.$previous.toggleClass("disabled",0>=f),this._controls.$next.toggleClass("disabled",f>=this._core.maximum())),this._controls.$previous.toggle(e.nav),this._controls.$next.toggle(e.nav),e.dots){if(b=this._pages.length-this._controls.$indicators.children().length,e.dotData&&0!==b){for(c=0;c<this._controls.$indicators.children().length;c++)d+=this._templates[this._core.relative(c)];this._controls.$indicators.html(d)}else b>0?(d=new Array(b+1).join(this._templates[0]),this._controls.$indicators.append(d)):0>b&&this._controls.$indicators.children().slice(b).remove();this._controls.$indicators.find(".active").removeClass("active"),this._controls.$indicators.children().eq(a.inArray(this.current(),this._pages)).addClass("active")}this._controls.$indicators.toggle(e.dots)},b.prototype.onTrigger=function(b){var c=this._core.settings;b.page={index:a.inArray(this.current(),this._pages),count:this._pages.length,size:c&&(c.center||c.autoWidth||c.dotData?1:c.dotsEach||c.items)}},b.prototype.current=function(){var b=this._core.relative(this._core.current());return a.grep(this._pages,function(a){return a.start<=b&&a.end>=b}).pop()},b.prototype.getPosition=function(b){var c,d,e=this._core.settings;return"page"==e.slideBy?(c=a.inArray(this.current(),this._pages),d=this._pages.length,b?++c:--c,c=this._pages[(c%d+d)%d].start):(c=this._core.relative(this._core.current()),d=this._core.items().length,b?c+=e.slideBy:c-=e.slideBy),c},b.prototype.next=function(b){a.proxy(this._overrides.to,this._core)(this.getPosition(!0),b)},b.prototype.prev=function(b){a.proxy(this._overrides.to,this._core)(this.getPosition(!1),b)},b.prototype.to=function(b,c,d){var e;d?a.proxy(this._overrides.to,this._core)(b,c):(e=this._pages.length,a.proxy(this._overrides.to,this._core)(this._pages[(b%e+e)%e].start,c))},a.fn.owlCarousel.Constructor.Plugins.Navigation=b}(window.Zepto||window.jQuery,window,document),function(a,b){"use strict";var c=function(d){this._core=d,this._hashes={},this.$element=this._core.$element,this._handlers={"initialized.owl.carousel":a.proxy(function(){"URLHash"==this._core.settings.startPosition&&a(b).trigger("hashchange.owl.navigation")},this),"prepared.owl.carousel":a.proxy(function(b){var c=a(b.content).find("[data-hash]").andSelf("[data-hash]").attr("data-hash");this._hashes[c]=b.content},this)},this._core.options=a.extend({},c.Defaults,this._core.options),this.$element.on(this._handlers),a(b).on("hashchange.owl.navigation",a.proxy(function(){var a=b.location.hash.substring(1),c=this._core.$stage.children(),d=this._hashes[a]&&c.index(this._hashes[a])||0;return a?void this._core.to(d,!1,!0):!1},this))};c.Defaults={URLhashListener:!1},c.prototype.destroy=function(){var c,d;a(b).off("hashchange.owl.navigation");for(c in this._handlers)this._core.$element.off(c,this._handlers[c]);for(d in Object.getOwnPropertyNames(this))"function"!=typeof this[d]&&(this[d]=null)},a.fn.owlCarousel.Constructor.Plugins.Hash=c}(window.Zepto||window.jQuery,window,document); 

/*!
Waypoints - 3.1.1
Copyright © 2011-2015 Caleb Troughton
Licensed under the MIT license.
https://github.com/imakewebthings/waypoints/blog/master/licenses.txt
*/
!function(){"use strict";function t(o){if(!o)throw new Error("No options passed to Waypoint constructor");if(!o.element)throw new Error("No element option passed to Waypoint constructor");if(!o.handler)throw new Error("No handler option passed to Waypoint constructor");this.key="waypoint-"+e,this.options=t.Adapter.extend({},t.defaults,o),this.element=this.options.element,this.adapter=new t.Adapter(this.element),this.callback=o.handler,this.axis=this.options.horizontal?"horizontal":"vertical",this.enabled=this.options.enabled,this.triggerPoint=null,this.group=t.Group.findOrCreate({name:this.options.group,axis:this.axis}),this.context=t.Context.findOrCreateByElement(this.options.context),t.offsetAliases[this.options.offset]&&(this.options.offset=t.offsetAliases[this.options.offset]),this.group.add(this),this.context.add(this),i[this.key]=this,e+=1}var e=0,i={};t.prototype.queueTrigger=function(t){this.group.queueTrigger(this,t)},t.prototype.trigger=function(t){this.enabled&&this.callback&&this.callback.apply(this,t)},t.prototype.destroy=function(){this.context.remove(this),this.group.remove(this),delete i[this.key]},t.prototype.disable=function(){return this.enabled=!1,this},t.prototype.enable=function(){return this.context.refresh(),this.enabled=!0,this},t.prototype.next=function(){return this.group.next(this)},t.prototype.previous=function(){return this.group.previous(this)},t.invokeAll=function(t){var e=[];for(var o in i)e.push(i[o]);for(var n=0,r=e.length;r>n;n++)e[n][t]()},t.destroyAll=function(){t.invokeAll("destroy")},t.disableAll=function(){t.invokeAll("disable")},t.enableAll=function(){t.invokeAll("enable")},t.refreshAll=function(){t.Context.refreshAll()},t.viewportHeight=function(){return window.innerHeight||document.documentElement.clientHeight},t.viewportWidth=function(){return document.documentElement.clientWidth},t.adapters=[],t.defaults={context:window,continuous:!0,enabled:!0,group:"default",horizontal:!1,offset:0},t.offsetAliases={"bottom-in-view":function(){return this.context.innerHeight()-this.adapter.outerHeight()},"right-in-view":function(){return this.context.innerWidth()-this.adapter.outerWidth()}},window.Waypoint=t}(),function(){"use strict";function t(t){window.setTimeout(t,1e3/60)}function e(t){this.element=t,this.Adapter=n.Adapter,this.adapter=new this.Adapter(t),this.key="waypoint-context-"+i,this.didScroll=!1,this.didResize=!1,this.oldScroll={x:this.adapter.scrollLeft(),y:this.adapter.scrollTop()},this.waypoints={vertical:{},horizontal:{}},t.waypointContextKey=this.key,o[t.waypointContextKey]=this,i+=1,this.createThrottledScrollHandler(),this.createThrottledResizeHandler()}var i=0,o={},n=window.Waypoint,r=window.onload;e.prototype.add=function(t){var e=t.options.horizontal?"horizontal":"vertical";this.waypoints[e][t.key]=t,this.refresh()},e.prototype.checkEmpty=function(){var t=this.Adapter.isEmptyObject(this.waypoints.horizontal),e=this.Adapter.isEmptyObject(this.waypoints.vertical);t&&e&&(this.adapter.off(".waypoints"),delete o[this.key])},e.prototype.createThrottledResizeHandler=function(){function t(){e.handleResize(),e.didResize=!1}var e=this;this.adapter.on("resize.waypoints",function(){e.didResize||(e.didResize=!0,n.requestAnimationFrame(t))})},e.prototype.createThrottledScrollHandler=function(){function t(){e.handleScroll(),e.didScroll=!1}var e=this;this.adapter.on("scroll.waypoints",function(){(!e.didScroll||n.isTouch)&&(e.didScroll=!0,n.requestAnimationFrame(t))})},e.prototype.handleResize=function(){n.Context.refreshAll()},e.prototype.handleScroll=function(){var t={},e={horizontal:{newScroll:this.adapter.scrollLeft(),oldScroll:this.oldScroll.x,forward:"right",backward:"left"},vertical:{newScroll:this.adapter.scrollTop(),oldScroll:this.oldScroll.y,forward:"down",backward:"up"}};for(var i in e){var o=e[i],n=o.newScroll>o.oldScroll,r=n?o.forward:o.backward;for(var s in this.waypoints[i]){var a=this.waypoints[i][s],l=o.oldScroll<a.triggerPoint,h=o.newScroll>=a.triggerPoint,p=l&&h,u=!l&&!h;(p||u)&&(a.queueTrigger(r),t[a.group.id]=a.group)}}for(var c in t)t[c].flushTriggers();this.oldScroll={x:e.horizontal.newScroll,y:e.vertical.newScroll}},e.prototype.innerHeight=function(){return this.element==this.element.window?n.viewportHeight():this.adapter.innerHeight()},e.prototype.remove=function(t){delete this.waypoints[t.axis][t.key],this.checkEmpty()},e.prototype.innerWidth=function(){return this.element==this.element.window?n.viewportWidth():this.adapter.innerWidth()},e.prototype.destroy=function(){var t=[];for(var e in this.waypoints)for(var i in this.waypoints[e])t.push(this.waypoints[e][i]);for(var o=0,n=t.length;n>o;o++)t[o].destroy()},e.prototype.refresh=function(){var t,e=this.element==this.element.window,i=this.adapter.offset(),o={};this.handleScroll(),t={horizontal:{contextOffset:e?0:i.left,contextScroll:e?0:this.oldScroll.x,contextDimension:this.innerWidth(),oldScroll:this.oldScroll.x,forward:"right",backward:"left",offsetProp:"left"},vertical:{contextOffset:e?0:i.top,contextScroll:e?0:this.oldScroll.y,contextDimension:this.innerHeight(),oldScroll:this.oldScroll.y,forward:"down",backward:"up",offsetProp:"top"}};for(var n in t){var r=t[n];for(var s in this.waypoints[n]){var a,l,h,p,u,c=this.waypoints[n][s],d=c.options.offset,f=c.triggerPoint,w=0,y=null==f;c.element!==c.element.window&&(w=c.adapter.offset()[r.offsetProp]),"function"==typeof d?d=d.apply(c):"string"==typeof d&&(d=parseFloat(d),c.options.offset.indexOf("%")>-1&&(d=Math.ceil(r.contextDimension*d/100))),a=r.contextScroll-r.contextOffset,c.triggerPoint=w+a-d,l=f<r.oldScroll,h=c.triggerPoint>=r.oldScroll,p=l&&h,u=!l&&!h,!y&&p?(c.queueTrigger(r.backward),o[c.group.id]=c.group):!y&&u?(c.queueTrigger(r.forward),o[c.group.id]=c.group):y&&r.oldScroll>=c.triggerPoint&&(c.queueTrigger(r.forward),o[c.group.id]=c.group)}}for(var g in o)o[g].flushTriggers();return this},e.findOrCreateByElement=function(t){return e.findByElement(t)||new e(t)},e.refreshAll=function(){for(var t in o)o[t].refresh()},e.findByElement=function(t){return o[t.waypointContextKey]},window.onload=function(){r&&r(),e.refreshAll()},n.requestAnimationFrame=function(e){var i=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||t;i.call(window,e)},n.Context=e}(),function(){"use strict";function t(t,e){return t.triggerPoint-e.triggerPoint}function e(t,e){return e.triggerPoint-t.triggerPoint}function i(t){this.name=t.name,this.axis=t.axis,this.id=this.name+"-"+this.axis,this.waypoints=[],this.clearTriggerQueues(),o[this.axis][this.name]=this}var o={vertical:{},horizontal:{}},n=window.Waypoint;i.prototype.add=function(t){this.waypoints.push(t)},i.prototype.clearTriggerQueues=function(){this.triggerQueues={up:[],down:[],left:[],right:[]}},i.prototype.flushTriggers=function(){for(var i in this.triggerQueues){var o=this.triggerQueues[i],n="up"===i||"left"===i;o.sort(n?e:t);for(var r=0,s=o.length;s>r;r+=1){var a=o[r];(a.options.continuous||r===o.length-1)&&a.trigger([i])}}this.clearTriggerQueues()},i.prototype.next=function(e){this.waypoints.sort(t);var i=n.Adapter.inArray(e,this.waypoints),o=i===this.waypoints.length-1;return o?null:this.waypoints[i+1]},i.prototype.previous=function(e){this.waypoints.sort(t);var i=n.Adapter.inArray(e,this.waypoints);return i?this.waypoints[i-1]:null},i.prototype.queueTrigger=function(t,e){this.triggerQueues[e].push(t)},i.prototype.remove=function(t){var e=n.Adapter.inArray(t,this.waypoints);e>-1&&this.waypoints.splice(e,1)},i.prototype.first=function(){return this.waypoints[0]},i.prototype.last=function(){return this.waypoints[this.waypoints.length-1]},i.findOrCreate=function(t){return o[t.axis][t.name]||new i(t)},n.Group=i}(),function(){"use strict";function t(t){this.$element=e(t)}var e=window.jQuery,i=window.Waypoint;e.each(["innerHeight","innerWidth","off","offset","on","outerHeight","outerWidth","scrollLeft","scrollTop"],function(e,i){t.prototype[i]=function(){var t=Array.prototype.slice.call(arguments);return this.$element[i].apply(this.$element,t)}}),e.each(["extend","inArray","isEmptyObject"],function(i,o){t[o]=e[o]}),i.adapters.push({name:"jquery",Adapter:t}),i.Adapter=t}(),function(){"use strict";function t(t){return function(){var i=[],o=arguments[0];return t.isFunction(arguments[0])&&(o=t.extend({},arguments[1]),o.handler=arguments[0]),this.each(function(){var n=t.extend({},o,{element:this});"string"==typeof n.context&&(n.context=t(this).closest(n.context)[0]),i.push(new e(n))}),i}}var e=window.Waypoint;window.jQuery&&(window.jQuery.fn.waypoint=t(window.jQuery)),window.Zepto&&(window.Zepto.fn.waypoint=t(window.Zepto))}();

/*!
* jquery.counterup.js 1.0
*
* Copyright 2013, Benjamin Intal http://gambit.ph @bfintal
* Released under the GPL v2 License
*
* Date: Nov 26, 2013
*/
(function($){"use strict";$.fn.counterUp=function(options){var settings=$.extend({'time':400,'delay':10},options);return this.each(function(){var $this=$(this);var $settings=settings;var counterUpper=function(){var nums=[];var divisions=$settings.time / $settings.delay;var num=$this.text();var isComma=/[0-9]+,[0-9]+/.test(num);num=num.replace(/,/g,'');var isInt=/^[0-9]+$/.test(num);var isFloat=/^[0-9]+\.[0-9]+$/.test(num);var decimalPlaces=isFloat?(num.split('.')[1]||[]).length:0;for(var i=divisions;i>=1;i--){var newNum=parseInt(num / divisions*i);if(isFloat){newNum=parseFloat(num / divisions*i).toFixed(decimalPlaces);}
if(isComma){while(/(\d+)(\d{3})/.test(newNum.toString())){newNum=newNum.toString().replace(/(\d+)(\d{3})/,'$1'+','+'$2');}}
nums.unshift(newNum);}
$this.data('counterup-nums',nums);$this.text('0');var f=function(){$this.text($this.data('counterup-nums').shift());if($this.data('counterup-nums').length){setTimeout($this.data('counterup-func'),$settings.delay);}else{delete $this.data('counterup-nums');$this.data('counterup-nums',null);$this.data('counterup-func',null);}};$this.data('counterup-func',f);setTimeout($this.data('counterup-func'),$settings.delay);};$this.waypoint(function(direction){counterUpper();this.destroy();},{offset:'100%'});});};})(jQuery);



jQuery(document).ready(function($) {
            $('.counter-info .counter-title').counterUp({
                delay: 10,
                time: 3000
            });

    $('.counter-box-info,.counter-box-image').matchHeight();
        });

$(document)["ready"](function() {
"use strict";
    var a = $(window);
$(".full-height, #main-intro .container")["height"](a["height"]());
        a["on"]("resize", function() {
            $(".full-height")["height"](a["height"]());
        });
$('#parallax-menu ul,.slide-in').onePageNav({
 currentClass: 'current',
 changeHash: false,
 scrollSpeed: 750
 });
    $('#parallax-menu ul').clone().appendTo('.scrolling-mobile-menu');
    $('.scrolling-mobile-menu-toggle').on('click', function() {
        $('body').toggleClass('scrolling-active');
        $('.overlay').fadeToggle(170);
    });
    
    // Search
    var searchTags = $('#hidden-search-tags .widget-content').html();
    $('#search-tags').html(searchTags);
    $('.search-toggle').on('click', function(e) {
        e.preventDefault();
        $('.search-overlay').css('display', 'flex').hide().fadeIn(200);
        $('.search-form input').focus();
    });
    $('.search-close').on('click', function() {
        $('.search-overlay').fadeOut(200);
    });
    
 });

//]]>
</script>

<!-- Facebook SDK -->
<script type='text/javascript'>
//<![CDATA[
(function(d, s, id) {
  var js, fjs = d.getElementsByTagName(s)[0];
  if (d.getElementById(id)) return;
  js = d.createElement(s); js.id = id;
  js.src = 'https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.0';
  fjs.parentNode.insertBefore(js, fjs);
}(document, 'script', 'facebook-jssdk'));
//]]>
</script>  
<!-- Overlay and Back To Top --> 
  <div class='overlay'/>
  <div class='back-top' title='Back to Top'/>
  <script type='text/javascript'>
 /*<![CDATA[*/
// Preloader
 
$(window).bind("load", function () {
    jQuery("#loader").fadeOut("slow");
    jQuery("#preloader").delay(0).fadeOut();
});
  /*]]>*/
</script>
</body>
</html>